import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useGameStore } from '../../stores/gameStore';
import { playClick, playMeow, playSuccess, playFail, playHint, playTick } from '../../utils/soundManager';
import { StarBurst } from '../effects/ParticleEffect';
import './GamePage.css';

const GamePage = () => {
  const {
    currentLevel,
    levels,
    gameState,
    soundEnabled,
    setCurrentScene,
    pauseGame,
    resumeGame,
    findCat,
    useHint,
    updateTimer,
    restartLevel
  } = useGameStore();

  const [showPauseMenu, setShowPauseMenu] = useState(false);
  const [foundCatAnimation, setFoundCatAnimation] = useState(null);
  const [clickEffects, setClickEffects] = useState([]);
  const [particleEffects, setParticleEffects] = useState([]);
  const timerRef = useRef(null);
  const gameAreaRef = useRef(null);

  const currentLevelData = levels.find(l => l.id === currentLevel);

  // 监听游戏状态变化播放音效
  useEffect(() => {
    if (!soundEnabled) return;

    // 检查是否刚完成关卡
    if (gameState.catsFound >= gameState.totalCats && gameState.catsFound > 0) {
      setTimeout(() => playSuccess(), 500); // 延迟播放成功音效
    }
  }, [gameState.catsFound, gameState.totalCats, soundEnabled]);

  // 监听时间结束
  useEffect(() => {
    if (!soundEnabled) return;

    if (gameState.timeLeft === 0 && gameState.catsFound < gameState.totalCats) {
      playFail();
    }
  }, [gameState.timeLeft, gameState.catsFound, gameState.totalCats, soundEnabled]);

  // 游戏计时器
  useEffect(() => {
    if (gameState.isPlaying && !gameState.isPaused) {
      timerRef.current = setInterval(() => {
        updateTimer();
        // 最后10秒播放倒计时音效
        if (soundEnabled && gameState.timeLeft <= 10 && gameState.timeLeft > 0) {
          playTick();
        }
      }, 1000);
    } else {
      clearInterval(timerRef.current);
    }

    return () => clearInterval(timerRef.current);
  }, [gameState.isPlaying, gameState.isPaused, gameState.timeLeft, soundEnabled, updateTimer]);

  // 处理暂停
  const handlePause = () => {
    if (soundEnabled) playClick();
    pauseGame();
    setShowPauseMenu(true);
  };

  const handleResume = () => {
    if (soundEnabled) playClick();
    resumeGame();
    setShowPauseMenu(false);
  };

  const handleBackToLevelSelect = () => {
    if (soundEnabled) playClick();
    setCurrentScene('levelSelect');
  };

  const handleRestart = () => {
    if (soundEnabled) playClick();
    restartLevel();
    setShowPauseMenu(false);
  };

  // 处理提示
  const handleUseHint = () => {
    if (gameState.hintsUsed < gameState.maxHints) {
      if (soundEnabled) playHint();
      useHint();
      // TODO: 显示提示效果
    }
  };

  // 处理点击游戏区域
  const handleGameAreaClick = (event) => {
    if (!gameState.isPlaying || gameState.isPaused) return;

    const rect = gameAreaRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // 播放点击音效
    if (soundEnabled) playClick();

    // 添加点击效果
    const clickEffect = {
      id: Date.now(),
      x: x,
      y: y
    };
    setClickEffects(prev => [...prev, clickEffect]);

    // 移除点击效果
    setTimeout(() => {
      setClickEffects(prev => prev.filter(effect => effect.id !== clickEffect.id));
    }, 1000);

    // 检查是否点击到猫咪
    checkCatClick(x, y);

    // 检查是否点击到可交互物品
    checkObjectClick(x, y);
  };

  // 检查猫咪点击
  const checkCatClick = (clickX, clickY) => {
    if (!currentLevelData) return;

    for (const cat of currentLevelData.cats) {
      if (gameState.foundCatIds.includes(cat.id)) continue;

      const catLeft = cat.x;
      const catTop = cat.y;
      const catRight = cat.x + cat.width;
      const catBottom = cat.y + cat.height;

      if (clickX >= catLeft && clickX <= catRight &&
          clickY >= catTop && clickY <= catBottom) {
        // 找到猫咪！
        if (soundEnabled) playMeow();
        findCat(cat.id);

        // 添加粒子效果
        const particleEffect = {
          id: Date.now(),
          x: clickX,
          y: clickY
        };
        setParticleEffects(prev => [...prev, particleEffect]);

        // 显示找到猫咪的动画
        setFoundCatAnimation({
          cat: cat,
          x: clickX,
          y: clickY
        });

        setTimeout(() => {
          setFoundCatAnimation(null);
        }, 2000);

        break;
      }
    }
  };

  // 检查物品点击
  const checkObjectClick = (clickX, clickY) => {
    if (!currentLevelData) return;

    for (const obj of currentLevelData.interactiveObjects) {
      const objLeft = obj.x;
      const objTop = obj.y;
      const objRight = obj.x + obj.width;
      const objBottom = obj.y + obj.height;

      if (clickX >= objLeft && clickX <= objRight && 
          clickY >= objTop && clickY <= objBottom) {
        // 物品被点击，添加交互效果
        // TODO: 添加物品移动动画
        break;
      }
    }
  };

  // 格式化时间显示
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!currentLevelData) {
    return <div>关卡数据加载中...</div>;
  }

  return (
    <div className="game-page">
      {/* 游戏头部状态栏 */}
      <div className="game-header">
        <div className="cats-found">
          <span className="label">找到:</span>
          <div className="cat-icons">
            {Array.from({ length: gameState.totalCats }, (_, index) => (
              <motion.span
                key={index}
                className={`cat-icon ${index < gameState.catsFound ? 'found' : ''}`}
                initial={index < gameState.catsFound ? { scale: 0 } : {}}
                animate={index < gameState.catsFound ? { scale: 1 } : {}}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              >
                🐱
              </motion.span>
            ))}
          </div>
          <span className="count">{gameState.catsFound}/{gameState.totalCats}</span>
        </div>

        <div className="level-title">
          <h2>{currentLevelData.name}</h2>
        </div>

        <div className="timer">
          <motion.span 
            className={`time ${gameState.timeLeft <= 10 ? 'warning' : ''}`}
            animate={gameState.timeLeft <= 10 ? { 
              scale: [1, 1.1, 1],
              color: ['#e74c3c', '#c0392b', '#e74c3c']
            } : {}}
            transition={{ duration: 0.5, repeat: gameState.timeLeft <= 10 ? Infinity : 0 }}
          >
            ⏰ {formatTime(gameState.timeLeft)}
          </motion.span>
        </div>
      </div>

      {/* 游戏主区域 */}
      <div 
        className="game-area"
        ref={gameAreaRef}
        onClick={handleGameAreaClick}
      >
        {/* 场景背景 */}
        <div className="scene-background">
          {currentLevelData.name === '温馨卧室' && <BedroomScene />}
          {currentLevelData.name === '静谧花园' && <GardenScene />}
        </div>

        {/* 可交互物品 */}
        {currentLevelData.interactiveObjects.map(obj => (
          <motion.div
            key={obj.id}
            className={`interactive-object ${obj.type}`}
            style={{
              left: obj.x,
              top: obj.y,
              width: obj.width,
              height: obj.height
            }}
            whileHover={{ scale: 1.02 }}
            animate={{ 
              scale: [1, 1.005, 1],
            }}
            transition={{ 
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* 隐藏的猫咪（仅用于调试，实际游戏中不显示） */}
        {process.env.NODE_ENV === 'development' && (
          <>
            {currentLevelData.cats.map(cat => (
              !gameState.foundCatIds.includes(cat.id) && (
                <div
                  key={cat.id}
                  className="debug-cat"
                  style={{
                    left: cat.x,
                    top: cat.y,
                    width: cat.width,
                    height: cat.height,
                    position: 'absolute',
                    border: '2px dashed red',
                    opacity: 0.3
                  }}
                />
              )
            ))}
          </>
        )}

        {/* 点击效果 */}
        <AnimatePresence>
          {clickEffects.map(effect => (
            <motion.div
              key={effect.id}
              className="click-effect"
              style={{
                left: effect.x - 15,
                top: effect.y - 15
              }}
              initial={{ scale: 0, opacity: 1 }}
              animate={{ scale: 2, opacity: 0 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 1 }}
            />
          ))}
        </AnimatePresence>

        {/* 找到猫咪动画 */}
        <AnimatePresence>
          {foundCatAnimation && (
            <motion.div
              className="found-cat-animation"
              style={{
                left: foundCatAnimation.x - 50,
                top: foundCatAnimation.y - 50
              }}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              transition={{ type: "spring", stiffness: 500, damping: 30 }}
            >
              <div className="found-cat-content">
                <motion.div
                  className="cat-celebration"
                  animate={{
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{ duration: 0.5, repeat: 3 }}
                >
                  🐱
                </motion.div>
                <div className="found-text">找到啦！</div>
                <div className="cat-name">{foundCatAnimation.cat.name}</div>
                <div className="stars">
                  {Array.from({ length: 5 }, (_, i) => (
                    <motion.span
                      key={i}
                      className="star"
                      initial={{ scale: 0, rotate: 0 }}
                      animate={{ scale: 1, rotate: 360 }}
                      transition={{ delay: i * 0.1, duration: 0.5 }}
                    >
                      ⭐
                    </motion.span>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 粒子效果 */}
        <AnimatePresence>
          {particleEffects.map(effect => (
            <StarBurst
              key={effect.id}
              x={effect.x}
              y={effect.y}
              onComplete={() => {
                setParticleEffects(prev => prev.filter(e => e.id !== effect.id));
              }}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* 游戏底部控制栏 */}
      <div className="game-controls">
        <motion.button
          className="hint-button btn btn-warning"
          onClick={handleUseHint}
          disabled={gameState.hintsUsed >= gameState.maxHints}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span>💡</span>
          <span>提示</span>
          <span className="hint-count">({gameState.maxHints - gameState.hintsUsed})</span>
        </motion.button>

        <motion.button
          className="pause-button btn btn-secondary"
          onClick={handlePause}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span>⏸️</span>
          <span>暂停</span>
        </motion.button>
      </div>

      {/* 暂停菜单 */}
      <AnimatePresence>
        {showPauseMenu && (
          <motion.div
            className="pause-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="pause-menu"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
            >
              <h3>游戏暂停</h3>
              <div className="pause-buttons">
                <button className="btn btn-primary" onClick={handleResume}>
                  继续游戏
                </button>
                <button className="btn btn-warning" onClick={handleRestart}>
                  重新开始
                </button>
                <button className="btn btn-secondary" onClick={handleBackToLevelSelect}>
                  返回关卡选择
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// 卧室场景组件
const BedroomScene = () => {
  const sceneStyle = {
    backgroundImage: `url('/images/scenes/bedroom.jpg')`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat'
  };

  return (
    <div className="bedroom-scene">
      <div className="bedroom-bg" style={sceneStyle}></div>
      <div className="bed"></div>
      <div className="window"></div>
      <div className="bookshelf"></div>
      <div className="curtain"></div>
    </div>
  );
};

// 花园场景组件
const GardenScene = () => {
  const sceneStyle = {
    backgroundImage: `url('/images/scenes/garden.jpg')`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat'
  };

  return (
    <div className="garden-scene">
      <div className="garden-bg" style={sceneStyle}></div>
      <div className="tree"></div>
      <div className="flowers"></div>
      <div className="bush"></div>
      <div className="grass"></div>
    </div>
  );
};

export default GamePage;
