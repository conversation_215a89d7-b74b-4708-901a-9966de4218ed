# 天才找猫猫 🐱

一款治愈系的找猫小游戏，让你在轻松愉快的氛围中寻找隐藏的可爱猫咪！

## 🎮 游戏特色

- **治愈画风**：温馨的马卡龙色彩搭配，营造轻松愉悦的游戏氛围
- **多样场景**：温馨卧室、静谧花园等不同风格的游戏场景
- **可爱猫咪**：每只猫咪都有独特的名字和隐藏方式
- **成就系统**：收集猫咪，解锁各种有趣的成就
- **音效反馈**：丰富的音效系统，增强游戏体验
- **响应式设计**：支持桌面和移动设备

## 🎯 游戏玩法

1. **选择关卡**：从猫咪相册中选择想要挑战的关卡
2. **寻找猫咪**：在限定时间内点击场景中的物品，寻找隐藏的猫咪
3. **使用提示**：遇到困难时可以使用提示道具
4. **收集成就**：完成关卡，收集可爱的猫咪，解锁各种成就

## 🛠️ 技术栈

- **React 18** - 现代化的前端框架
- **Vite** - 快速的构建工具
- **Framer Motion** - 流畅的动画效果
- **Zustand** - 轻量级状态管理
- **Web Audio API** - 原生音效系统

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 🎨 游戏页面

- **首页**：游戏启动页，包含开始按钮和设置选项
- **关卡选择**：猫咪相册风格的关卡选择界面
- **游戏主页面**：核心找猫游戏界面
- **结算页面**：显示游戏结果和得分
- **收藏页面**：猫咪图鉴和成就展示

## 🏆 成就系统

- **初次相遇**：找到第一只猫咪
- **猫语者**：找到5只猫咪
- **闪电猎手**：在30秒内完成一关
- **独立探索者**：不使用提示完成一关
- **猫咪大师**：完成所有关卡

## 🎵 音效系统

游戏包含丰富的音效反馈：
- 点击音效
- 猫咪叫声
- 成功/失败音效
- 倒计时提示音
- 提示音效

可在设置中开启/关闭音效。

## 📱 响应式支持

游戏完全支持移动设备，在手机和平板上也能获得良好的游戏体验。

## 🎯 游戏目标

《天才找猫猫》旨在为玩家提供一个轻松、治愈的游戏体验。通过寻找可爱的猫咪，让玩家在忙碌的生活中获得片刻的宁静和快乐。

---

**开始你的找猫之旅吧！** 🐾
