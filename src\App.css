/* 天才找猫猫游戏样式 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Comic Sans MS', cursive, sans-serif;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.game-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 通用按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: linear-gradient(45deg, #ff6b6b, #ffa726);
  color: white;
}

.btn-secondary {
  background: linear-gradient(45deg, #74b9ff, #0984e3);
  color: white;
}

.btn-success {
  background: linear-gradient(45deg, #00b894, #00cec9);
  color: white;
}

.btn-warning {
  background: linear-gradient(45deg, #fdcb6e, #e17055);
  color: white;
}

/* 卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

.bounce-animation {
  animation: bounce 2s infinite;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .btn {
    padding: 10px 20px;
    font-size: 14px;
  }

  .card {
    padding: 15px;
    margin: 10px;
  }
}
