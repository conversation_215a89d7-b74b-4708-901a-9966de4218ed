.result-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 50%, #ffd3a5 100%);
  padding: 2rem;
}

.result-container {
  max-width: 500px;
  width: 100%;
}

/* 成功结果页面 */
.success-result {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 3px solid #00b894;
}

.success-header {
  margin-bottom: 2rem;
  position: relative;
}

.success-title {
  font-size: 2.5rem;
  color: #00b894;
  margin-bottom: 1rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.celebration-emoji {
  font-size: 3rem;
  position: absolute;
  top: -20px;
  right: 20px;
}

/* 猫咪展示 */
.cats-showcase {
  margin-bottom: 2rem;
}

.cats-showcase h3 {
  color: #2d3436;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.cats-grid {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.cat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(116, 185, 255, 0.1);
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cat-avatar {
  font-size: 2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.cat-name {
  font-size: 0.9rem;
  color: #2d3436;
  font-weight: bold;
}

/* 得分统计 */
.score-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(253, 203, 110, 0.1);
  border-radius: 15px;
  border: 2px solid #fdcb6e;
}

.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
  font-size: 1rem;
}

.score-item:last-child {
  margin-bottom: 0;
}

.score-label {
  color: #636e72;
  font-weight: 500;
}

.score-value {
  color: #2d3436;
  font-weight: bold;
}

.score-item.main-score {
  font-size: 1.2rem;
  padding: 0.8rem;
  background: rgba(0, 184, 148, 0.1);
  border-radius: 10px;
  border: 2px solid #00b894;
}

.score-item.main-score .score-value {
  color: #00b894;
  font-size: 1.3rem;
}

.score-item.total-score {
  font-size: 1.1rem;
  border-top: 2px solid #ddd;
  padding-top: 0.8rem;
  margin-top: 0.8rem;
}

/* 解锁奖励 */
.unlock-reward {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 15px;
  border: 2px solid #ff6b6b;
  margin-bottom: 2rem;
}

.reward-icon {
  font-size: 2rem;
}

.reward-text {
  color: #2d3436;
  font-weight: bold;
  font-size: 1rem;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  min-width: 140px;
  justify-content: center;
}

.next-button,
.complete-button {
  flex: 1;
  max-width: 200px;
}

.share-button {
  flex: 0.8;
  max-width: 160px;
}

/* 失败结果页面 */
.failure-result {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 3px solid #e17055;
}

.failure-header {
  margin-bottom: 2rem;
}

.sad-cat {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.failure-title {
  font-size: 2rem;
  color: #e17055;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.failure-subtitle {
  font-size: 1.1rem;
  color: #636e72;
  margin: 0;
}

/* 失败统计 */
.failure-stats {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(225, 112, 85, 0.1);
  border-radius: 15px;
  border: 2px solid #e17055;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
  font-size: 1rem;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-item span:first-child {
  color: #636e72;
  font-weight: 500;
}

.stat-item span:last-child {
  color: #2d3436;
  font-weight: bold;
}

.retry-button {
  flex: 1;
  max-width: 180px;
}

.back-button {
  flex: 1;
  max-width: 180px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .result-page {
    padding: 1rem;
  }
  
  .success-result,
  .failure-result {
    padding: 1.5rem;
  }
  
  .success-title {
    font-size: 2rem;
  }
  
  .celebration-emoji {
    font-size: 2.5rem;
    top: -15px;
    right: 15px;
  }
  
  .cats-grid {
    gap: 0.8rem;
  }
  
  .cat-item {
    padding: 0.8rem;
  }
  
  .cat-avatar {
    font-size: 1.5rem;
  }
  
  .score-section {
    padding: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .action-buttons .btn {
    width: 100%;
    max-width: 250px;
  }
  
  .sad-cat {
    font-size: 3rem;
  }
  
  .failure-title {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .success-title {
    font-size: 1.8rem;
  }
  
  .celebration-emoji {
    font-size: 2rem;
  }
  
  .cats-grid {
    gap: 0.5rem;
  }
  
  .cat-item {
    padding: 0.6rem;
    min-width: 80px;
  }
  
  .cat-avatar {
    font-size: 1.3rem;
  }
  
  .cat-name {
    font-size: 0.8rem;
  }
  
  .score-item {
    font-size: 0.9rem;
  }
  
  .action-buttons .btn {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }
}
