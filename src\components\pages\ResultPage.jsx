import React from 'react';
import { motion } from 'framer-motion';
import { useGameStore } from '../../stores/gameStore';
import './ResultPage.css';

const ResultPage = () => {
  const {
    currentLevel,
    levels,
    gameState,
    totalScore,
    setCurrentScene,
    startGame,
    restartLevel
  } = useGameStore();

  const currentLevelData = levels.find(l => l.id === currentLevel);
  const isSuccess = gameState.catsFound >= gameState.totalCats;
  const timeUsed = currentLevelData ? currentLevelData.timeLimit - gameState.timeLeft : 0;
  const score = isSuccess ? Math.max(100, 1000 - timeUsed * 10 - gameState.hintsUsed * 50) : 0;

  const handleNextLevel = () => {
    const nextLevelId = currentLevel + 1;
    const nextLevel = levels.find(l => l.id === nextLevelId);
    if (nextLevel && nextLevel.unlocked) {
      startGame(nextLevelId);
    } else {
      setCurrentScene('levelSelect');
    }
  };

  const handleRetry = () => {
    restartLevel();
  };

  const handleBackToLevelSelect = () => {
    setCurrentScene('levelSelect');
  };

  const handleShare = () => {
    // TODO: 实现分享功能
    console.log('分享游戏成绩');
  };

  if (!currentLevelData) {
    return <div>加载中...</div>;
  }

  return (
    <div className="result-page">
      <div className="result-container">
        {isSuccess ? (
          // 成功页面
          <motion.div
            className="success-result"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ type: "spring", stiffness: 260, damping: 20 }}
          >
            {/* 成功标题 */}
            <motion.div
              className="success-header"
              initial={{ y: -50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              <motion.h1
                className="success-title"
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                太棒了！
              </motion.h1>
              <motion.div
                className="celebration-emoji"
                animate={{
                  y: [0, -20, 0],
                  rotate: [0, 360]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                🎉
              </motion.div>
            </motion.div>

            {/* 找到的猫咪展示 */}
            <motion.div
              className="cats-showcase"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.8 }}
            >
              <h3>找到的猫咪们</h3>
              <div className="cats-grid">
                {currentLevelData.cats.map((cat, index) => (
                  <motion.div
                    key={cat.id}
                    className="cat-item"
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{
                      delay: 0.8 + index * 0.2,
                      type: "spring",
                      stiffness: 260,
                      damping: 20
                    }}
                    whileHover={{
                      scale: 1.1,
                      rotate: [0, 10, -10, 0]
                    }}
                  >
                    <div className="cat-avatar">🐱</div>
                    <div className="cat-name">{cat.name}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* 得分统计 */}
            <motion.div
              className="score-section"
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 1.2, duration: 0.6 }}
            >
              <div className="score-item">
                <span className="score-label">用时:</span>
                <span className="score-value">{timeUsed}秒</span>
              </div>
              <div className="score-item">
                <span className="score-label">提示使用:</span>
                <span className="score-value">{gameState.hintsUsed}次</span>
              </div>
              <div className="score-item main-score">
                <span className="score-label">本关得分:</span>
                <motion.span
                  className="score-value"
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1, repeat: Infinity }}
                >
                  +{score}分
                </motion.span>
              </div>
              <div className="score-item total-score">
                <span className="score-label">总分:</span>
                <span className="score-value">{totalScore}分</span>
              </div>
            </motion.div>

            {/* 解锁奖励 */}
            {currentLevel < levels.length && (
              <motion.div
                className="unlock-reward"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 1.5, type: "spring", stiffness: 260, damping: 20 }}
              >
                <div className="reward-icon">🎁</div>
                <div className="reward-text">
                  解锁新关卡: {levels.find(l => l.id === currentLevel + 1)?.name}
                </div>
              </motion.div>
            )}

            {/* 操作按钮 */}
            <motion.div
              className="action-buttons"
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 1.8, duration: 0.6 }}
            >
              {currentLevel < levels.length ? (
                <motion.button
                  className="btn btn-primary next-button"
                  onClick={handleNextLevel}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span>下一关</span>
                  <span>→</span>
                </motion.button>
              ) : (
                <motion.button
                  className="btn btn-success complete-button"
                  onClick={handleBackToLevelSelect}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span>🏆</span>
                  <span>全部完成！</span>
                </motion.button>
              )}
              
              <motion.button
                className="btn btn-secondary share-button"
                onClick={handleShare}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span>📱</span>
                <span>分享成绩</span>
              </motion.button>
            </motion.div>
          </motion.div>
        ) : (
          // 失败页面
          <motion.div
            className="failure-result"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ type: "spring", stiffness: 260, damping: 20 }}
          >
            {/* 失败标题 */}
            <motion.div
              className="failure-header"
              initial={{ y: -50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              <motion.div
                className="sad-cat"
                animate={{
                  rotate: [0, -10, 10, 0],
                  scale: [1, 0.9, 1]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                😿
              </motion.div>
              <h1 className="failure-title">差一点点！</h1>
              <p className="failure-subtitle">再试一次？</p>
            </motion.div>

            {/* 统计信息 */}
            <motion.div
              className="failure-stats"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.8 }}
            >
              <div className="stat-item">
                <span>找到猫咪:</span>
                <span>{gameState.catsFound}/{gameState.totalCats}</span>
              </div>
              <div className="stat-item">
                <span>剩余时间:</span>
                <span>{gameState.timeLeft}秒</span>
              </div>
            </motion.div>

            {/* 操作按钮 */}
            <motion.div
              className="action-buttons"
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.9, duration: 0.6 }}
            >
              <motion.button
                className="btn btn-primary retry-button"
                onClick={handleRetry}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span>🔄</span>
                <span>重玩本关</span>
              </motion.button>
              
              <motion.button
                className="btn btn-secondary back-button"
                onClick={handleBackToLevelSelect}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span>←</span>
                <span>返回关卡选择</span>
              </motion.button>
            </motion.div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default ResultPage;
