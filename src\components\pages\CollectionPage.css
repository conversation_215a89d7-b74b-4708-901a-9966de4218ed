.collection-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 50%, #fff0f5 100%);
  display: flex;
  flex-direction: column;
}

/* 头部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;
  font-size: 1rem;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 1.8rem;
  color: #2d3436;
  margin: 0;
}

.page-title span:first-child {
  font-size: 2rem;
}

.header-spacer {
  width: 120px;
}

/* 标签切换 */
.tab-switcher {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding: 1.5rem 2rem;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: 2px solid #ddd;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 500;
}

.tab-button.active {
  border-color: #00b894;
  background: #00b894;
  color: white;
}

.tab-button span:first-child {
  font-size: 1.2rem;
}

.count {
  font-size: 0.8rem;
  opacity: 0.8;
  background: rgba(0, 0, 0, 0.1);
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
}

.tab-button.active .count {
  background: rgba(255, 255, 255, 0.2);
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 0 2rem 2rem;
  overflow-y: auto;
}

/* 猫咪收藏 */
.cats-collection {
  max-width: 1200px;
  margin: 0 auto;
}

.level-section {
  margin-bottom: 3rem;
}

.level-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #ddd;
}

.level-header h3 {
  color: #2d3436;
  font-size: 1.3rem;
  margin: 0;
}

.level-progress {
  background: #00b894;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: bold;
}

.cats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
}

.cat-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border: 2px solid transparent;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.cat-card.unlocked {
  border-color: #00b894;
}

.cat-card.unlocked:hover {
  transform: translateY(-5px);
}

.cat-card.locked {
  opacity: 0.6;
  cursor: not-allowed;
  border-color: #ddd;
}

.cat-avatar {
  font-size: 3rem;
  margin-bottom: 1rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.cat-info {
  text-align: center;
}

.cat-name {
  font-size: 1.1rem;
  font-weight: bold;
  color: #2d3436;
  margin-bottom: 0.5rem;
}

.cat-description {
  font-size: 0.9rem;
  color: #636e72;
  line-height: 1.4;
}

.lock-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  opacity: 0.5;
}

/* 成就收藏 */
.achievements-collection {
  max-width: 800px;
  margin: 0 auto;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.achievement-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  position: relative;
  border: 2px solid transparent;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.achievement-card.unlocked {
  border-color: #fdcb6e;
}

.achievement-card.unlocked:hover {
  transform: translateY(-3px);
}

.achievement-card.locked {
  opacity: 0.6;
  border-color: #ddd;
}

.achievement-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.achievement-info {
  flex: 1;
}

.achievement-name {
  font-size: 1.1rem;
  font-weight: bold;
  color: #2d3436;
  margin-bottom: 0.3rem;
}

.achievement-description {
  font-size: 0.9rem;
  color: #636e72;
  line-height: 1.4;
}

.unlock-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  background: #00b894;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  font-weight: bold;
}

/* 猫咪详情弹窗 */
.cat-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.cat-detail-modal {
  background: white;
  border-radius: 25px;
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-bottom: 2px solid #f1f2f6;
}

.modal-header h3 {
  margin: 0;
  color: #2d3436;
  font-size: 1.5rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: #636e72;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: #f1f2f6;
  color: #2d3436;
}

.modal-content {
  padding: 2rem;
  text-align: center;
}

.cat-large-avatar {
  font-size: 5rem;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.cat-details {
  text-align: left;
  background: #f8f9fa;
  border-radius: 15px;
  padding: 1.5rem;
}

.cat-details p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.cat-details p:last-child {
  margin-bottom: 0;
}

.cat-details strong {
  color: #2d3436;
}

.modal-actions {
  padding: 1.5rem 2rem;
  border-top: 2px solid #f1f2f6;
  text-align: center;
}

.modal-actions .btn {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 1rem;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .tab-switcher {
    padding: 1rem;
    gap: 0.8rem;
  }
  
  .tab-button {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }
  
  .content-area {
    padding: 0 1rem 1rem;
  }
  
  .cats-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
  }
  
  .cat-card {
    padding: 1rem;
  }
  
  .cat-avatar {
    font-size: 2.5rem;
  }
  
  .achievements-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .achievement-card {
    padding: 1rem;
  }
  
  .achievement-icon {
    font-size: 2rem;
  }
  
  .cat-detail-overlay {
    padding: 1rem;
  }
  
  .modal-header {
    padding: 1rem 1.5rem;
  }
  
  .modal-content {
    padding: 1.5rem;
  }
  
  .cat-large-avatar {
    font-size: 4rem;
  }
  
  .header-spacer {
    width: 80px;
  }
}
