.level-select-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 50%, #ffd3a5 100%);
  display: flex;
  flex-direction: column;
}

/* 头部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;
  font-size: 1rem;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 1.8rem;
  color: #2d3436;
  margin: 0;
}

.page-title span:first-child {
  font-size: 2rem;
}

.header-spacer {
  width: 120px; /* 与back-button宽度相同，保持居中 */
}

/* 关卡容器 */
.levels-container {
  flex: 1;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.levels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  width: 100%;
}

/* 关卡卡片 */
.level-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border: 3px solid transparent;
}

.level-card.unlocked {
  border-color: #00b894;
}

.level-card.locked {
  opacity: 0.6;
  cursor: not-allowed;
  border-color: #ddd;
}

.level-card.completed {
  border-color: #fdcb6e;
  background: linear-gradient(135deg, rgba(253, 203, 110, 0.1), rgba(255, 255, 255, 0.95));
}

/* 关卡图片预览 */
.level-image {
  height: 200px;
  position: relative;
  overflow: hidden;
}

.level-preview {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(135deg, #74b9ff, #0984e3);
}

.scene-preview {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 卧室预览 */
.bedroom-preview {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  position: relative;
}

.bedroom-preview .bed {
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: 60px;
  height: 40px;
  background: #8b4513;
  border-radius: 10px;
}

.bedroom-preview .bed::after {
  content: '';
  position: absolute;
  top: -10px;
  left: 5px;
  width: 50px;
  height: 15px;
  background: #fff;
  border-radius: 5px;
}

.bedroom-preview .window {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 60px;
  background: #74b9ff;
  border-radius: 5px;
  border: 3px solid #2d3436;
}

.bedroom-preview .cat-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  opacity: 0.7;
}

/* 花园预览 */
.garden-preview {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #a8e6cf, #88d8a3);
  position: relative;
}

.garden-preview .tree {
  position: absolute;
  bottom: 20px;
  left: 30px;
  width: 20px;
  height: 60px;
  background: #8b4513;
  border-radius: 10px;
}

.garden-preview .tree::after {
  content: '';
  position: absolute;
  top: -20px;
  left: -15px;
  width: 50px;
  height: 40px;
  background: #00b894;
  border-radius: 50%;
}

.garden-preview .flowers {
  position: absolute;
  bottom: 20px;
  right: 30px;
  display: flex;
  gap: 5px;
}

.garden-preview .flowers::before,
.garden-preview .flowers::after {
  content: '🌸';
  font-size: 1.2rem;
}

.garden-preview .cat-hint {
  position: absolute;
  top: 40%;
  right: 40%;
  font-size: 1.5rem;
  opacity: 0.8;
}

/* 锁定预览 */
.locked-preview {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #b2bec3, #636e72);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.lock-icon {
  font-size: 3rem;
  opacity: 0.8;
}

.paw-prints {
  display: flex;
  gap: 0.5rem;
  opacity: 0.6;
}

.paw-prints span {
  font-size: 1rem;
}

/* 完成徽章 */
.completed-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 40px;
  height: 40px;
  background: #00b894;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
}

/* 关卡信息 */
.level-info {
  padding: 1.5rem;
}

.level-name {
  font-size: 1.3rem;
  color: #2d3436;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.level-description {
  color: #636e72;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.level-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  background: rgba(116, 185, 255, 0.1);
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
}

.stat.best-time {
  background: rgba(253, 203, 110, 0.2);
}

.stat-icon {
  font-size: 1rem;
}

.unlock-hint {
  text-align: center;
  color: #636e72;
  font-size: 0.9rem;
  font-style: italic;
}

/* 点击提示 */
.play-hint {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 15px;
  font-size: 0.8rem;
  pointer-events: none;
}

/* 首次提示 */
.first-time-hint {
  margin-top: 2rem;
  text-align: center;
  color: #636e72;
}

.hint-arrow {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 1rem;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .levels-container {
    padding: 1rem;
  }
  
  .levels-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .level-card {
    max-width: 400px;
    margin: 0 auto;
  }
  
  .level-image {
    height: 150px;
  }
  
  .header-spacer {
    width: 80px;
  }
}
