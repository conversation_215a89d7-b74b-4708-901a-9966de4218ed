import { create } from 'zustand';

// 游戏状态管理
export const useGameStore = create((set, get) => ({
  // 游戏基础状态
  currentScene: 'home', // 'home', 'levelSelect', 'game', 'result', 'collection'
  currentLevel: 1,
  unlockedLevels: [1],
  
  // 玩家数据
  totalScore: 0,
  achievements: [],
  collectedCats: [],
  
  // 游戏设置
  soundEnabled: true,
  musicEnabled: true,
  
  // 当前关卡状态
  gameState: {
    isPlaying: false,
    isPaused: false,
    timeLeft: 60,
    catsFound: 0,
    totalCats: 3,
    foundCatIds: [],
    hintsUsed: 0,
    maxHints: 3,
  },
  
  // 关卡数据
  levels: [
    {
      id: 1,
      name: '温馨卧室',
      description: '在这个舒适的卧室里找到3只调皮的猫咪',
      timeLimit: 60,
      totalCats: 3,
      background: '/images/bedroom.jpg',
      unlocked: true,
      completed: false,
      bestTime: null,
      cats: [
        { id: 'cat1', x: 150, y: 200, width: 40, height: 30, hidden: true, name: '橘胖' },
        { id: 'cat2', x: 300, y: 150, width: 35, height: 25, hidden: true, name: '雪球' },
        { id: 'cat3', x: 450, y: 300, width: 38, height: 28, hidden: true, name: '小黑' },
      ],
      interactiveObjects: [
        { id: 'pillow', x: 100, y: 180, width: 80, height: 60, type: 'pillow' },
        { id: 'curtain', x: 280, y: 50, width: 100, height: 200, type: 'curtain' },
        { id: 'bookshelf', x: 400, y: 250, width: 120, height: 150, type: 'bookshelf' },
      ]
    },
    {
      id: 2,
      name: '静谧花园',
      description: '在美丽的花园中寻找4只躲藏的猫咪',
      timeLimit: 90,
      totalCats: 4,
      background: '/images/garden.jpg',
      unlocked: false,
      completed: false,
      bestTime: null,
      cats: [
        { id: 'cat4', x: 120, y: 250, width: 35, height: 30, hidden: true, name: '花花' },
        { id: 'cat5', x: 350, y: 180, width: 40, height: 32, hidden: true, name: '绿眼' },
        { id: 'cat6', x: 500, y: 320, width: 38, height: 28, hidden: true, name: '草丛' },
        { id: 'cat7', x: 200, y: 100, width: 42, height: 35, hidden: true, name: '树影' },
      ],
      interactiveObjects: [
        { id: 'bush1', x: 100, y: 230, width: 80, height: 70, type: 'bush' },
        { id: 'tree', x: 180, y: 50, width: 100, height: 180, type: 'tree' },
        { id: 'flowers', x: 320, y: 160, width: 90, height: 60, type: 'flowers' },
        { id: 'bush2', x: 480, y: 300, width: 85, height: 75, type: 'bush' },
      ]
    }
  ],
  
  // Actions
  setCurrentScene: (scene) => set({ currentScene: scene }),
  
  setCurrentLevel: (level) => set({ currentLevel: level }),
  
  unlockLevel: (levelId) => set((state) => ({
    unlockedLevels: [...new Set([...state.unlockedLevels, levelId])]
  })),
  
  startGame: (levelId) => {
    const level = get().levels.find(l => l.id === levelId);
    if (!level) return;
    
    set({
      currentLevel: levelId,
      currentScene: 'game',
      gameState: {
        isPlaying: true,
        isPaused: false,
        timeLeft: level.timeLimit,
        catsFound: 0,
        totalCats: level.totalCats,
        foundCatIds: [],
        hintsUsed: 0,
        maxHints: 3,
      }
    });
  },
  
  pauseGame: () => set((state) => ({
    gameState: { ...state.gameState, isPaused: true, isPlaying: false }
  })),
  
  resumeGame: () => set((state) => ({
    gameState: { ...state.gameState, isPaused: false, isPlaying: true }
  })),
  
  findCat: (catId) => {
    const state = get();
    const newFoundCats = [...state.gameState.foundCatIds, catId];
    const newCatsFound = newFoundCats.length;
    
    set({
      gameState: {
        ...state.gameState,
        foundCatIds: newFoundCats,
        catsFound: newCatsFound,
      }
    });
    
    // 检查是否完成关卡
    if (newCatsFound >= state.gameState.totalCats) {
      get().completeLevel();
    }
  },
  
  useHint: () => set((state) => ({
    gameState: {
      ...state.gameState,
      hintsUsed: Math.min(state.gameState.hintsUsed + 1, state.gameState.maxHints)
    }
  })),
  
  updateTimer: () => {
    const state = get();
    if (!state.gameState.isPlaying || state.gameState.isPaused) return;
    
    const newTimeLeft = Math.max(0, state.gameState.timeLeft - 1);
    set({
      gameState: { ...state.gameState, timeLeft: newTimeLeft }
    });
    
    // 时间用完，游戏失败
    if (newTimeLeft === 0) {
      get().gameOver();
    }
  },
  
  completeLevel: () => {
    const state = get();
    const currentLevel = state.levels.find(l => l.id === state.currentLevel);
    const timeUsed = currentLevel.timeLimit - state.gameState.timeLeft;
    const score = Math.max(100, 1000 - timeUsed * 10 - state.gameState.hintsUsed * 50);

    // 收集找到的猫咪
    const foundCats = currentLevel.cats.filter(cat =>
      state.gameState.foundCatIds.includes(cat.id)
    );
    foundCats.forEach(cat => {
      get().collectCat({
        ...cat,
        levelName: currentLevel.name,
        foundAt: new Date().toISOString()
      });
    });

    // 检查成就
    get().checkAchievements(timeUsed);

    // 更新关卡完成状态
    const updatedLevels = state.levels.map(level => {
      if (level.id === state.currentLevel) {
        return {
          ...level,
          completed: true,
          bestTime: level.bestTime ? Math.min(level.bestTime, timeUsed) : timeUsed
        };
      }
      return level;
    });

    // 解锁下一关
    const nextLevelId = state.currentLevel + 1;
    const newUnlockedLevels = [...new Set([...state.unlockedLevels, nextLevelId])];

    set({
      currentScene: 'result',
      totalScore: state.totalScore + score,
      unlockedLevels: newUnlockedLevels,
      levels: updatedLevels,
      gameState: { ...state.gameState, isPlaying: false }
    });
  },

  checkAchievements: (timeUsed) => {
    const state = get();

    // 速度成就
    if (timeUsed <= 30) {
      get().addAchievement('speed_finder');
    }

    // 无提示成就
    if (state.gameState.hintsUsed === 0) {
      get().addAchievement('no_hints');
    }

    // 猫咪收集成就
    const totalCatsFound = state.collectedCats.length + state.gameState.foundCatIds.length;
    if (totalCatsFound >= 1) {
      get().addAchievement('first_cat');
    }
    if (totalCatsFound >= 5) {
      get().addAchievement('cat_lover');
    }

    // 全关卡完成成就
    const allCompleted = state.levels.every(level => level.completed);
    if (allCompleted) {
      get().addAchievement('all_levels');
    }
  },
  
  gameOver: () => set((state) => ({
    currentScene: 'result',
    gameState: { ...state.gameState, isPlaying: false }
  })),
  
  restartLevel: () => {
    const state = get();
    get().startGame(state.currentLevel);
  },
  
  toggleSound: () => set((state) => ({ soundEnabled: !state.soundEnabled })),
  
  toggleMusic: () => set((state) => ({ musicEnabled: !state.musicEnabled })),
  
  addAchievement: (achievement) => set((state) => ({
    achievements: [...new Set([...state.achievements, achievement])]
  })),
  
  collectCat: (catData) => set((state) => ({
    collectedCats: [...state.collectedCats.filter(c => c.id !== catData.id), catData]
  })),
}));
