.home-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
  overflow: hidden;
}

/* 背景装饰元素 */
.background-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-element {
  position: absolute;
  font-size: 2rem;
  opacity: 0.6;
}

.yarn-ball {
  top: 20%;
  left: 15%;
}

.yarn-ball::before {
  content: '🧶';
}

.fish-bone {
  top: 60%;
  right: 20%;
}

.fish-bone::before {
  content: '🐟';
}

.paw-print {
  bottom: 30%;
  left: 10%;
}

.paw-print::before {
  content: '🐾';
}

/* 主要内容 */
.home-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  z-index: 2;
  max-width: 400px;
  width: 100%;
  padding: 2rem;
}

/* 游戏Logo */
.game-logo {
  text-align: center;
  margin-bottom: 1rem;
}

.game-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.title-text {
  font-size: 2.5rem;
  font-weight: bold;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease infinite;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.cat-emoji {
  font-size: 2rem;
}

.game-subtitle {
  font-size: 1.1rem;
  color: #2d3436;
  opacity: 0.8;
  font-weight: 500;
}

/* 开始游戏按钮 */
.start-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  padding: 1rem 2rem;
  border-radius: 30px;
  background: linear-gradient(45deg, #ff6b6b, #ffa726);
  color: white;
  border: none;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
  position: relative;
  overflow: hidden;
}

.start-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.start-button:hover::before {
  left: 100%;
}

.button-text {
  font-weight: bold;
}

.button-cat {
  font-size: 1rem;
}

/* 底部按钮组 */
.bottom-buttons {
  display: flex;
  gap: 1rem;
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;
}

.ranking-button,
.collection-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  padding: 1rem;
  border-radius: 20px;
  min-width: 120px;
  font-size: 0.9rem;
  position: relative;
}

.ranking-button span:first-child,
.collection-button span:first-child {
  font-size: 1.5rem;
}

.score-display {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.2rem;
}

/* 设置面板 */
.settings-panel {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 300px;
}

.settings-title {
  text-align: center;
  font-weight: bold;
  color: #2d3436;
  margin-bottom: 0.8rem;
  font-size: 1rem;
}

.settings-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.setting-toggle {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  padding: 0.8rem;
  border: 2px solid #ddd;
  border-radius: 15px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
  font-size: 0.8rem;
}

.setting-toggle.active {
  border-color: #00b894;
  background: #00b894;
  color: white;
}

.setting-toggle span:first-child {
  font-size: 1.2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title-text {
    font-size: 2rem;
  }
  
  .home-content {
    padding: 1rem;
    gap: 1.5rem;
  }
  
  .start-button {
    font-size: 1rem;
    padding: 0.8rem 1.5rem;
  }
  
  .bottom-buttons {
    gap: 0.8rem;
  }
  
  .ranking-button,
  .collection-button {
    min-width: 100px;
    padding: 0.8rem;
    font-size: 0.8rem;
  }
  
  .floating-element {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .title-text {
    font-size: 1.8rem;
  }
  
  .bottom-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .ranking-button,
  .collection-button {
    width: 100%;
    max-width: 200px;
  }
}
