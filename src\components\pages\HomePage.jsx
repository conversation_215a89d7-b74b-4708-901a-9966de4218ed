import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useGameStore } from '../../stores/gameStore';
import { playClick, playWhoosh, initSounds } from '../../utils/soundManager';
import './HomePage.css';

const HomePage = () => {
  const { setCurrentScene, totalScore, soundEnabled, musicEnabled, toggleSound, toggleMusic } = useGameStore();

  // 初始化音效系统
  useEffect(() => {
    const handleFirstInteraction = () => {
      initSounds();
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('touchstart', handleFirstInteraction);
    };

    document.addEventListener('click', handleFirstInteraction);
    document.addEventListener('touchstart', handleFirstInteraction);

    return () => {
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('touchstart', handleFirstInteraction);
    };
  }, []);

  const handleStartGame = () => {
    if (soundEnabled) playWhoosh();
    setCurrentScene('levelSelect');
  };

  const handleShowRanking = () => {
    if (soundEnabled) playClick();
    // TODO: 实现排行榜功能
    console.log('显示排行榜');
  };

  const handleShowCollection = () => {
    if (soundEnabled) playWhoosh();
    setCurrentScene('collection');
  };

  const handleToggleSound = () => {
    toggleSound();
    if (!soundEnabled) playClick(); // 只在开启时播放音效
  };

  const handleToggleMusic = () => {
    if (soundEnabled) playClick();
    toggleMusic();
  };

  return (
    <div className="home-page">
      {/* 背景装饰元素 */}
      <div className="background-decorations">
        <motion.div 
          className="floating-element yarn-ball"
          animate={{ 
            y: [0, -20, 0],
            rotate: [0, 360]
          }}
          transition={{ 
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div 
          className="floating-element fish-bone"
          animate={{ 
            y: [0, -15, 0],
            x: [0, 10, 0]
          }}
          transition={{ 
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />
        <motion.div 
          className="floating-element paw-print"
          animate={{ 
            scale: [1, 1.1, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{ 
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.5
          }}
        />
      </div>

      {/* 主要内容 */}
      <div className="home-content">
        {/* 游戏Logo */}
        <motion.div 
          className="game-logo"
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ 
            type: "spring",
            stiffness: 260,
            damping: 20,
            duration: 1
          }}
        >
          <h1 className="game-title">
            <span className="title-text">天才找猫猫</span>
            <motion.div 
              className="cat-emoji"
              animate={{ 
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              🐱
            </motion.div>
          </h1>
          <p className="game-subtitle">寻找隐藏的可爱猫咪</p>
        </motion.div>

        {/* 开始游戏按钮 */}
        <motion.button
          className="start-button btn btn-primary"
          onClick={handleStartGame}
          whileHover={{ 
            scale: 1.05,
            boxShadow: "0 10px 25px rgba(255, 107, 107, 0.3)"
          }}
          whileTap={{ scale: 0.95 }}
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.6 }}
        >
          <span className="button-text">开始游戏</span>
          <motion.span 
            className="button-cat"
            animate={{ 
              x: [0, 5, 0],
            }}
            transition={{ 
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            🐾
          </motion.span>
        </motion.button>

        {/* 底部按钮组 */}
        <motion.div 
          className="bottom-buttons"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.6 }}
        >
          <motion.button
            className="ranking-button btn btn-secondary"
            onClick={handleShowRanking}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <span>🏆</span>
            <span>排行榜</span>
            <div className="score-display">总分: {totalScore}</div>
          </motion.button>

          <motion.button
            className="collection-button btn btn-success"
            onClick={handleShowCollection}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <span>📚</span>
            <span>猫咪图鉴</span>
          </motion.button>
        </motion.div>

        {/* 设置按钮 */}
        <motion.div 
          className="settings-panel"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.6 }}
        >
          <div className="settings-title">设置</div>
          <div className="settings-controls">
            <motion.button
              className={`setting-toggle ${soundEnabled ? 'active' : ''}`}
              onClick={handleToggleSound}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <span>{soundEnabled ? '🔊' : '🔇'}</span>
              <span>音效</span>
            </motion.button>

            <motion.button
              className={`setting-toggle ${musicEnabled ? 'active' : ''}`}
              onClick={handleToggleMusic}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <span>{musicEnabled ? '🎵' : '🎵'}</span>
              <span>音乐</span>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default HomePage;
