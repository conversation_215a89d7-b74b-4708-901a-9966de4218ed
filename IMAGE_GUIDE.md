# 🎨 天才找猫猫 - 图片素材指南

## 📁 目录结构

请将图片素材按以下结构放置在 `public/images/` 目录下：

```
public/
└── images/
    ├── scenes/          # 场景背景图
    ├── cats/           # 猫咪图片
    ├── objects/        # 可交互物品
    ├── ui/             # UI元素
    └── effects/        # 特效图片
```

## 🖼️ 图片规格要求

### 场景背景图 (`/images/scenes/`)
- **尺寸**: 1920x1080 或 1280x720 (16:9比例)
- **格式**: JPG 或 PNG
- **大小**: 建议 < 500KB
- **风格**: 温馨、治愈、卡通风格

**需要的场景图片**:
- `bedroom.jpg` - 温馨卧室场景
- `garden.jpg` - 静谧花园场景
- `street.jpg` - 热闹街道场景（可选）
- `balcony.jpg` - 阳台场景（可选）

### 猫咪图片 (`/images/cats/`)
- **尺寸**: 128x128 或 256x256 (正方形)
- **格式**: PNG (支持透明背景)
- **大小**: 建议 < 50KB
- **风格**: 可爱、Q版、表情丰富

**需要的猫咪图片**:
- `orange-cat.png` - 橘猫
- `white-cat.png` - 白猫
- `black-cat.png` - 黑猫
- `tabby-cat.png` - 虎斑猫
- `persian-cat.png` - 波斯猫（可选）
- `siamese-cat.png` - 暹罗猫（可选）

### 可交互物品 (`/images/objects/`)
- **尺寸**: 根据物品大小，建议 64x64 到 256x256
- **格式**: PNG (支持透明背景)
- **大小**: 建议 < 30KB
- **风格**: 与场景风格一致

**需要的物品图片**:
- `pillow.png` - 枕头
- `curtain.png` - 窗帘
- `bookshelf.png` - 书架
- `bush.png` - 灌木丛
- `tree.png` - 树木
- `flowers.png` - 花朵
- `sofa.png` - 沙发（可选）
- `table.png` - 桌子（可选）

### UI元素 (`/images/ui/`)
- **尺寸**: 根据用途，32x32 到 512x512
- **格式**: PNG (支持透明背景)
- **大小**: 建议 < 20KB

**需要的UI图片**:
- `logo.png` - 游戏Logo (512x512)
- `cat-paw.png` - 猫爪图标 (64x64)
- `heart.png` - 爱心图标 (32x32)
- `star.png` - 星星图标 (32x32)
- `trophy.png` - 奖杯图标 (64x64)

### 特效图片 (`/images/effects/`)
- **尺寸**: 32x32 到 128x128
- **格式**: PNG (支持透明背景)
- **大小**: 建议 < 10KB

**需要的特效图片**:
- `sparkle.png` - 闪光特效
- `star.png` - 星星特效
- `heart.png` - 爱心特效
- `confetti.png` - 彩带特效

## 🎨 设计建议

### 色彩搭配
- **主色调**: 温暖的马卡龙色系
- **卧室场景**: 粉色、米色、浅蓝色
- **花园场景**: 绿色、黄色、粉色
- **猫咪**: 自然的毛色，眼睛可以用亮色突出

### 风格统一
- 保持卡通、可爱的整体风格
- 避免过于写实或恐怖的元素
- 线条柔和，色彩饱和度适中
- 符合"治愈系"的设计理念

## 🔧 如何添加图片

### 1. 放置图片文件
将图片文件放在对应的 `public/images/` 子目录中

### 2. 在代码中引用
```jsx
// 直接使用路径
<img src="/images/cats/orange-cat.png" alt="橘猫" />

// 在CSS中使用
.bedroom-bg {
  background-image: url('/images/scenes/bedroom.jpg');
}

// 使用图片管理器
import { ImagePaths } from '../utils/imageManager';
<img src={ImagePaths.cats.orange} alt="橘猫" />
```

### 3. 更新游戏数据
在 `src/stores/gameStore.js` 中更新关卡数据，添加图片路径：

```javascript
cats: [
  { 
    id: 'cat1', 
    x: 150, 
    y: 200, 
    width: 40, 
    height: 30, 
    hidden: true, 
    name: '橘胖',
    image: '/images/cats/orange-cat.png'  // 添加图片路径
  }
]
```

## 📱 响应式考虑

- 提供不同尺寸的图片版本（可选）
- 使用适当的压缩以减少加载时间
- 考虑移动设备的显示效果

## 🚀 性能优化

- 使用图片压缩工具减小文件大小
- 考虑使用WebP格式（现代浏览器支持）
- 实现图片懒加载（已提供工具函数）
- 预加载关键图片

## 🎯 免费素材资源推荐

- **Freepik**: https://www.freepik.com/
- **Unsplash**: https://unsplash.com/
- **Pixabay**: https://pixabay.com/
- **OpenGameArt**: https://opengameart.org/
- **Kenney**: https://kenney.nl/assets

## 📝 注意事项

1. 确保使用的图片有合适的版权许可
2. 保持文件命名的一致性
3. 定期检查图片加载是否正常
4. 备份原始高质量图片文件

---

**开始创建你的猫咪世界吧！** 🐱✨
