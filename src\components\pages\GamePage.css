.game-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #2d3436;
  position: relative;
}

/* 游戏头部状态栏 */
.game-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.cats-found {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-weight: bold;
  color: #2d3436;
}

.cat-icons {
  display: flex;
  gap: 0.3rem;
}

.cat-icon {
  font-size: 1.2rem;
  opacity: 0.3;
  transition: all 0.3s ease;
}

.cat-icon.found {
  opacity: 1;
  filter: drop-shadow(0 0 5px rgba(255, 193, 7, 0.5));
}

.count {
  font-size: 1.1rem;
  color: #00b894;
}

.level-title h2 {
  margin: 0;
  color: #2d3436;
  font-size: 1.3rem;
  text-align: center;
}

.timer {
  font-weight: bold;
  font-size: 1.1rem;
}

.time {
  color: #00b894;
}

.time.warning {
  color: #e74c3c;
  font-weight: bold;
}

/* 游戏主区域 */
.game-area {
  flex: 1;
  position: relative;
  cursor: crosshair;
  overflow: hidden;
  min-height: 400px;
}

.scene-background {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* 可交互物品 */
.interactive-object {
  position: absolute;
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.interactive-object:hover {
  filter: brightness(1.1);
}

.interactive-object.pillow {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid #ddd;
}

.interactive-object.curtain {
  background: rgba(225, 112, 85, 0.6);
  border: 2px solid #e17055;
}

.interactive-object.bookshelf {
  background: rgba(139, 69, 19, 0.6);
  border: 2px solid #8b4513;
}

.interactive-object.bush {
  background: rgba(0, 184, 148, 0.6);
  border: 2px solid #00b894;
  border-radius: 50%;
}

.interactive-object.tree {
  background: rgba(139, 69, 19, 0.6);
  border: 2px solid #8b4513;
}

.interactive-object.flowers {
  background: rgba(253, 121, 168, 0.6);
  border: 2px solid #fd79a8;
  border-radius: 50%;
}

/* 卧室场景 */
.bedroom-scene {
  width: 100%;
  height: 100%;
  position: relative;
}

.bedroom-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
  position: absolute;
}

.bedroom-scene .bed {
  position: absolute;
  bottom: 10%;
  left: 10%;
  width: 150px;
  height: 100px;
  background: #8b4513;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.bedroom-scene .bed::after {
  content: '';
  position: absolute;
  top: -20px;
  left: 10px;
  width: 130px;
  height: 30px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.bedroom-scene .window {
  position: absolute;
  top: 15%;
  right: 15%;
  width: 120px;
  height: 150px;
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  border-radius: 10px;
  border: 5px solid #2d3436;
  box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.3);
}

.bedroom-scene .window::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 2px;
  background: #2d3436;
  transform: translateY(-50%);
}

.bedroom-scene .bookshelf {
  position: absolute;
  bottom: 15%;
  right: 25%;
  width: 100px;
  height: 120px;
  background: #8b4513;
  border-radius: 5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.bedroom-scene .bookshelf::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 5px;
  width: 90px;
  height: 15px;
  background: #2d3436;
  border-radius: 2px;
  box-shadow: 0 25px 0 #2d3436, 0 50px 0 #2d3436;
}

.bedroom-scene .curtain {
  position: absolute;
  top: 10%;
  right: 10%;
  width: 80px;
  height: 200px;
  background: linear-gradient(90deg, #e17055, #fdcb6e);
  border-radius: 0 0 20px 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 花园场景 */
.garden-scene {
  width: 100%;
  height: 100%;
  position: relative;
}

.garden-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 50%, #68c3a3 100%);
  position: absolute;
}

.garden-scene .tree {
  position: absolute;
  bottom: 20%;
  left: 20%;
  width: 40px;
  height: 120px;
  background: #8b4513;
  border-radius: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.garden-scene .tree::after {
  content: '';
  position: absolute;
  top: -40px;
  left: -30px;
  width: 100px;
  height: 80px;
  background: #00b894;
  border-radius: 50%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.garden-scene .flowers {
  position: absolute;
  bottom: 25%;
  right: 25%;
  width: 80px;
  height: 60px;
  background: radial-gradient(circle, #fd79a8, #fdcb6e);
  border-radius: 50%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.garden-scene .flowers::before {
  content: '🌸🌺🌻';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.2rem;
}

.garden-scene .bush {
  position: absolute;
  bottom: 15%;
  left: 50%;
  width: 120px;
  height: 80px;
  background: #00b894;
  border-radius: 50%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateX(-50%);
}

.garden-scene .grass {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 20%;
  background: linear-gradient(180deg, transparent, #00b894);
  opacity: 0.3;
}

/* 点击效果 */
.click-effect {
  position: absolute;
  width: 30px;
  height: 30px;
  border: 3px solid #ff6b6b;
  border-radius: 50%;
  pointer-events: none;
  z-index: 100;
}

/* 找到猫咪动画 */
.found-cat-animation {
  position: absolute;
  z-index: 200;
  pointer-events: none;
}

.found-cat-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 1rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 2px solid #00b894;
}

.cat-celebration {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.found-text {
  font-size: 1.2rem;
  font-weight: bold;
  color: #00b894;
  margin-bottom: 0.3rem;
}

.cat-name {
  font-size: 1rem;
  color: #2d3436;
  margin-bottom: 0.5rem;
}

.stars {
  display: flex;
  justify-content: center;
  gap: 0.2rem;
}

.star {
  font-size: 1rem;
}

/* 游戏控制栏 */
.game-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
}

.hint-button,
.pause-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
}

.hint-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.hint-count {
  font-size: 0.8rem;
  opacity: 0.8;
}

/* 暂停菜单 */
.pause-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.pause-menu {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 300px;
  width: 90%;
}

.pause-menu h3 {
  margin-bottom: 1.5rem;
  color: #2d3436;
  font-size: 1.5rem;
}

.pause-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.pause-buttons .btn {
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
}

/* 调试模式下的猫咪边框 */
.debug-cat {
  pointer-events: none;
  z-index: 50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .game-header {
    padding: 0.8rem 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .cats-found {
    gap: 0.5rem;
    font-size: 0.9rem;
  }

  .cat-icon {
    font-size: 1rem;
  }

  .level-title h2 {
    font-size: 1.1rem;
  }

  .timer {
    font-size: 1rem;
  }

  .game-controls {
    padding: 0.8rem 1rem;
    gap: 0.8rem;
  }

  .hint-button,
  .pause-button {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }

  .found-cat-content {
    padding: 0.8rem;
  }

  .cat-celebration {
    font-size: 2.5rem;
  }

  .found-text {
    font-size: 1rem;
  }

  /* 场景元素缩放 */
  .bedroom-scene .bed {
    width: 120px;
    height: 80px;
  }

  .bedroom-scene .window {
    width: 100px;
    height: 120px;
  }

  .bedroom-scene .bookshelf {
    width: 80px;
    height: 100px;
  }

  .garden-scene .tree {
    width: 30px;
    height: 100px;
  }

  .garden-scene .flowers {
    width: 60px;
    height: 45px;
  }

  .garden-scene .bush {
    width: 100px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .game-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .pause-menu {
    padding: 1.5rem;
  }

  .pause-buttons .btn {
    padding: 0.8rem;
    font-size: 0.9rem;
  }
}
