import{jsxs as t,jsx as e}from"react/jsx-runtime";import{createContext as n,useContext as i,useMemo as s,Fragment as o,createElement as r,useRef as a,useCallback as l,useLayoutEffect as h,useEffect as u,useInsertionEffect as c,forwardRef as d,useId as p,Component as m}from"react";function f(t,e){-1===t.indexOf(e)&&t.push(e)}function y(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const g=(t,e,n)=>n>e?e:n<t?t:n;const v={},x=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function T(t){return"object"==typeof t&&null!==t}const w=t=>/^0[^.\s]+$/u.test(t);function P(t){let e;return()=>(void 0===e&&(e=t()),e)}const S=t=>t,b=(t,e)=>n=>e(t(n)),A=(...t)=>t.reduce(b),E=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i};class V{constructor(){this.subscriptions=[]}add(t){return f(this.subscriptions,t),()=>y(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const M=t=>1e3*t,C=t=>t/1e3;function D(t,e){return e?t*(1e3/e):0}const k=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function R(t,e,n,i){if(t===e&&n===i)return S;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=k(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:k(s(t),e,i)}const L=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,j=t=>e=>1-t(1-e),B=R(.33,1.53,.69,.99),F=j(B),O=L(F),I=t=>(t*=2)<1?.5*F(t):.5*(2-Math.pow(2,-10*(t-1))),U=t=>1-Math.sin(Math.acos(t)),N=j(U),W=L(U),$=R(.42,0,1,1),Y=R(0,0,.58,1),X=R(.42,0,.58,1),K=t=>Array.isArray(t)&&"number"==typeof t[0],z={linear:S,easeIn:$,easeInOut:X,easeOut:Y,circIn:U,circInOut:W,circOut:N,backIn:F,backInOut:O,backOut:B,anticipate:I},H=t=>{if(K(t)){t.length;const[e,n,i,s]=t;return R(e,n,i,s)}return"string"==typeof t?z[t]:t},G=n({}),q=n({strict:!1}),Z=n({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),_=n({});function J(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function Q(t){return"string"==typeof t||Array.isArray(t)}const tt=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],et=["initial",...tt];function nt(t){return J(t.animate)||et.some(e=>Q(t[e]))}function it(t){return Boolean(nt(t)||t.variants)}function st(t){const{initial:e,animate:n}=function(t,e){if(nt(t)){const{initial:e,animate:n}=t;return{initial:!1===e||Q(e)?e:void 0,animate:Q(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,i(_));return s(()=>({initial:e,animate:n}),[ot(e),ot(n)])}function ot(t){return Array.isArray(t)?t.join(" "):t}const rt=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],at={value:null,addProjectionMetrics:null};function lt(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=rt.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){r.has(e)&&(u.schedule(e),t()),l++,e(a)}const u={schedule:(t,e=!1,o=!1)=>{const a=o&&s?n:i;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[n,i]=[i,n],n.forEach(h),e&&at.value&&at.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,u.process(t)))}};return u}(o,e?n:void 0),t),{}),{setup:a,read:l,resolveKeyframes:h,preUpdate:u,update:c,preRender:d,render:p,postRender:m}=r,f=()=>{const o=v.useManualTiming?s.timestamp:performance.now();n=!1,v.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),h.process(s),u.process(s),c.process(s),d.process(s),p.process(s),m.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(f))};return{schedule:rt.reduce((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(f)),a.schedule(e,o,r)),e},{}),cancel:t=>{for(let e=0;e<rt.length;e++)r[rt[e]].cancel(t)},state:s,steps:r}}const{schedule:ht,cancel:ut,state:ct,steps:dt}=lt("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:S,!0);let pt;function mt(){pt=void 0}const ft={now:()=>(void 0===pt&&ft.set(ct.isProcessing||v.useManualTiming?ct.timestamp:performance.now()),pt),set:t=>{pt=t,queueMicrotask(mt)}},yt=t=>e=>"string"==typeof e&&e.startsWith(t),gt=yt("--"),vt=yt("var(--"),xt=t=>!!vt(t)&&Tt.test(t.split("/*")[0].trim()),Tt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,wt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Pt={...wt,transform:t=>g(0,1,t)},St={...wt,default:1},bt=t=>Math.round(1e5*t)/1e5,At=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const Et=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Vt=(t,e)=>n=>Boolean("string"==typeof n&&Et.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),Mt=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(At);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},Ct={...wt,transform:t=>Math.round((t=>g(0,255,t))(t))},Dt={test:Vt("rgb","red"),parse:Mt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+Ct.transform(t)+", "+Ct.transform(e)+", "+Ct.transform(n)+", "+bt(Pt.transform(i))+")"};const kt={test:Vt("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:Dt.transform},Rt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),Lt=Rt("deg"),jt=Rt("%"),Bt=Rt("px"),Ft=Rt("vh"),Ot=Rt("vw"),It=(()=>({...jt,parse:t=>jt.parse(t)/100,transform:t=>jt.transform(100*t)}))(),Ut={test:Vt("hsl","hue"),parse:Mt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+jt.transform(bt(e))+", "+jt.transform(bt(n))+", "+bt(Pt.transform(i))+")"},Nt={test:t=>Dt.test(t)||kt.test(t)||Ut.test(t),parse:t=>Dt.test(t)?Dt.parse(t):Ut.test(t)?Ut.parse(t):kt.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?Dt.transform(t):Ut.transform(t),getAnimatableNone:t=>{const e=Nt.parse(t);return e.alpha=0,Nt.transform(e)}},Wt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const $t="number",Yt="color",Xt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Kt(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(Xt,t=>(Nt.test(t)?(i.color.push(o),s.push(Yt),n.push(Nt.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push($t),n.push(parseFloat(t))),++o,"${}")).split("${}");return{values:n,split:r,indexes:i,types:s}}function zt(t){return Kt(t).values}function Ht(t){const{split:e,types:n}=Kt(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===$t?bt(t[o]):e===Yt?Nt.transform(t[o]):t[o]}return s}}const Gt=t=>"number"==typeof t?0:Nt.test(t)?Nt.getAnimatableNone(t):t;const qt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(At)?.length||0)+(t.match(Wt)?.length||0)>0},parse:zt,createTransformer:Ht,getAnimatableNone:function(t){const e=zt(t);return Ht(t)(e.map(Gt))}};function Zt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function _t(t,e){return n=>n>0?e:t}const Jt=(t,e,n)=>t+(e-t)*n,Qt=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},te=[kt,Dt,Ut];function ee(t){const e=(n=t,te.find(t=>t.test(n)));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===Ut&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=Zt(a,i,t+1/3),o=Zt(a,i,t),r=Zt(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const ne=(t,e)=>{const n=ee(t),i=ee(e);if(!n||!i)return _t(t,e);const s={...n};return t=>(s.red=Qt(n.red,i.red,t),s.green=Qt(n.green,i.green,t),s.blue=Qt(n.blue,i.blue,t),s.alpha=Jt(n.alpha,i.alpha,t),Dt.transform(s))},ie=new Set(["none","hidden"]);function se(t,e){return n=>Jt(t,e,n)}function oe(t){return"number"==typeof t?se:"string"==typeof t?xt(t)?_t:Nt.test(t)?ne:le:Array.isArray(t)?re:"object"==typeof t?Nt.test(t)?ne:ae:_t}function re(t,e){const n=[...t],i=n.length,s=t.map((t,n)=>oe(t)(t,e[n]));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function ae(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=oe(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const le=(t,e)=>{const n=qt.createTransformer(e),i=Kt(t),s=Kt(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?ie.has(t)&&!s.values.length||ie.has(e)&&!i.values.length?function(t,e){return ie.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):A(re(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}(i,s),s.values),n):_t(t,e)};function he(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Jt(t,e,n);return oe(t)(t,e)}const ue=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>ht.update(e,t),stop:()=>ut(e),now:()=>ct.isProcessing?ct.timestamp:ft.now()}},ce=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${i.substring(0,i.length-2)})`},de=2e4;function pe(t){let e=0;let n=t.next(e);for(;!n.done&&e<de;)e+=50,n=t.next(e);return e>=de?1/0:e}function me(t,e,n){const i=Math.max(e-5,0);return D(n-t(i),e-i)}const fe=100,ye=10,ge=1,ve=0,xe=800,Te=.3,we=.3,Pe={granular:.01,default:2},Se={granular:.005,default:.5},be=.01,Ae=10,Ee=.05,Ve=1,Me=.001;function Ce({duration:t=xe,bounce:e=Te,velocity:n=ve,mass:i=ge}){let s,o,r=1-e;r=g(Ee,Ve,r),t=g(be,Ae,C(t)),r<1?(s=e=>{const i=e*r,s=i*t,o=i-n,a=ke(e,r),l=Math.exp(-s);return Me-o/a*l},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),h=ke(Math.pow(e,2),r);return(-s(e)+Me>0?-1:1)*((o-a)*l)/h}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let n=1;n<De;n++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=M(t),isNaN(a))return{stiffness:fe,damping:ye,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const De=12;function ke(t,e){return t*Math.sqrt(1-e*e)}const Re=["duration","bounce"],Le=["stiffness","damping","mass"];function je(t,e){return e.some(e=>void 0!==t[e])}function Be(t=we,e=Te){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:h,mass:u,duration:c,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:ve,stiffness:fe,damping:ye,mass:ge,isResolvedFromDuration:!1,...t};if(!je(t,Le)&&je(t,Re))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*g(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:ge,stiffness:s,damping:o}}else{const n=Ce(t);e={...e,...n,mass:ge},e.isResolvedFromDuration=!0}return e}({...n,velocity:-C(n.velocity||0)}),m=d||0,f=h/(2*Math.sqrt(l*u)),y=r-o,v=C(Math.sqrt(l/u)),x=Math.abs(y)<5;let T;if(i||(i=x?Pe.granular:Pe.default),s||(s=x?Se.granular:Se.default),f<1){const t=ke(v,f);T=e=>{const n=Math.exp(-f*v*e);return r-n*((m+f*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}}else if(1===f)T=t=>r-Math.exp(-v*t)*(y+(m+v*y)*t);else{const t=v*Math.sqrt(f*f-1);T=e=>{const n=Math.exp(-f*v*e),i=Math.min(t*e,300);return r-n*((m+f*v*y)*Math.sinh(i)+t*y*Math.cosh(i))/t}}const w={calculatedDuration:p&&c||null,next:t=>{const e=T(t);if(p)a.done=t>=c;else{let n=0===t?m:0;f<1&&(n=0===t?M(m):me(T,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(pe(w),de),e=ce(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function Fe({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:h=.5,restSpeed:u}){const c=t[0],d={done:!1,value:c},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=c+m,y=void 0===r?f:r(f);y!==f&&(m=y-c);const g=t=>-m*Math.exp(-t/i),v=t=>y+g(t),x=t=>{const e=g(t),n=v(t);d.done=Math.abs(e)<=h,d.value=d.done?y:n};let T,w;const P=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(T=t,w=Be({keyframes:[d.value,p(d.value)],velocity:me(v,t,d.value),damping:s,stiffness:o,restDelta:h,restSpeed:u}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==T||(e=!0,x(t),P(t)),void 0!==T&&t>=T?w.next(t-T):(!e&&x(t),d)}}}function Oe(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(e.length,1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||v.mix||he,o=t.length-1;for(let n=0;n<o;n++){let o=s(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||S:e;o=A(t,o)}i.push(o)}return i}(e,i,s),l=a.length,h=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=E(t[i],t[i+1],n);return a[i](s)};return n?e=>h(g(t[0],t[o-1],e)):h}function Ie(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=E(0,e,i);t.push(Jt(n,1,s))}}(e,t.length-1),e}function Ue({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(i)?i.map(H):H(i),o={done:!1,value:e[0]},r=function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:Ie(e),t),a=Oe(r,e,{ease:Array.isArray(s)?s:(l=e,h=s,l.map(()=>h||X).splice(0,l.length-1))});var l,h;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}Be.applyToOptions=t=>{const e=function(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(pe(i),de);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:C(s)}}(t,100,Be);return t.ease=e.ease,t.duration=M(e.duration),t.type="keyframes",t};const Ne=t=>null!==t;function We(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(Ne),r=s<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return r&&void 0!==i?i:o[r]}const $e={decay:Fe,inertia:Fe,tween:Ue,keyframes:Ue,spring:Be};function Ye(t){"string"==typeof t.type&&(t.type=$e[t.type])}class Xe{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Ke=t=>t/100;class ze extends Xe{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==ft.now()&&this.tick(ft.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Ye(t);const{type:e=Ue,repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||Ue;a!==Ue&&"number"!=typeof r[0]&&(this.mixKeyframes=A(Ke,he(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=pe(l));const{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:r,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:h,repeat:u,repeatType:c,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?y<0:y>i;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let x=this.currentTime,T=n;if(u){const t=Math.min(this.currentTime,i)/r;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,u+1);Boolean(e%2)&&("reverse"===c?(n=1-n,d&&(n-=d/r)):"mirror"===c&&(T=o)),x=g(0,1,n)*r}const w=v?{done:!1,value:h[0]}:T.next(x);s&&(w.value=s(w.value));let{done:P}=w;v||null===a||(P=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return S&&p!==Fe&&(w.value=We(h,this.options,f,this.speed)),m&&m(w.value),S&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return C(this.calculatedDuration)}get time(){return C(this.currentTime)}set time(t){t=M(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(ft.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=C(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=ue,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(ft.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}const He=t=>180*t/Math.PI,Ge=t=>{const e=He(Math.atan2(t[1],t[0]));return Ze(e)},qe={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Ge,rotateZ:Ge,skewX:t=>He(Math.atan(t[1])),skewY:t=>He(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ze=t=>((t%=360)<0&&(t+=360),t),_e=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Je=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Qe={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:_e,scaleY:Je,scale:t=>(_e(t)+Je(t))/2,rotateX:t=>Ze(He(Math.atan2(t[6],t[5]))),rotateY:t=>Ze(He(Math.atan2(-t[2],t[0]))),rotateZ:Ge,rotate:Ge,skewX:t=>He(Math.atan(t[4])),skewY:t=>He(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function tn(t){return t.includes("scale")?1:0}function en(t,e){if(!t||"none"===t)return tn(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=Qe,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=qe,s=e}if(!s)return tn(e);const o=i[e],r=s[1].split(",").map(nn);return"function"==typeof o?o(r):r[o]}function nn(t){return parseFloat(t.trim())}const sn=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],on=(()=>new Set(sn))(),rn=t=>t===wt||t===Bt,an=new Set(["x","y","z"]),ln=sn.filter(t=>!an.has(t));const hn={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>en(e,"x"),y:(t,{transform:e})=>en(e,"y")};hn.translateX=hn.x,hn.translateY=hn.y;const un=new Set;let cn=!1,dn=!1,pn=!1;function mn(){if(dn){const t=Array.from(un).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return ln.forEach(n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}dn=!1,cn=!1,un.forEach(t=>t.complete(pn)),un.clear()}function fn(){un.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(dn=!0)})}class yn{constructor(t,e,n,i,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(un.add(this),cn||(cn=!0,ht.read(fn),ht.resolveKeyframes(mn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=i?.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),un.delete(this)}cancel(){"scheduled"===this.state&&(un.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const gn=P(()=>void 0!==window.ScrollTimeline),vn={};function xn(t,e){const n=P(t);return()=>vn[e]??n()}const Tn=xn(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),wn=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Pn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:wn([0,.65,.55,1]),circOut:wn([.55,0,1,.45]),backIn:wn([.31,.01,.66,-.59]),backOut:wn([.33,1.53,.69,.99])};function Sn(t,e){return t?"function"==typeof t?Tn()?ce(t,e):"ease-out":K(t)?wn(t):Array.isArray(t)?t.map(t=>Sn(t,e)||Pn.easeOut):Pn[t]:void 0}function bn(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},h=void 0){const u={[e]:n};l&&(u.offset=l);const c=Sn(a,s);Array.isArray(c)&&(u.easing=c);const d={delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};h&&(d.pseudoElement=h);return t.animate(u,d)}function An(t){return"function"==typeof t&&"applyToOptions"in t}class En extends Xe{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:i,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,t.type;const l=function({type:t,...e}){return An(t)&&Tn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=bn(e,n,i,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=We(i,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return C(Number(t))}get time(){return C(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=M(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&gn()?(this.animation.timeline=t,S):e(this)}}const Vn={anticipate:I,backInOut:O,circInOut:W};function Mn(t){"string"==typeof t.ease&&t.ease in Vn&&(t.ease=Vn[t.ease])}class Cn extends En{constructor(t){Mn(t),Ye(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:i,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new ze({...o,autoplay:!1}),a=M(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const Dn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!qt.test(t)&&"0"!==t||t.startsWith("url(")));function kn(t){t.duration=0,t.type}const Rn=new Set(["opacity","clipPath","filter","transform"]),Ln=P(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class jn extends Xe{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=ft.now();const c={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:h,...u},d=h?.KeyframeResolver||yn;this.keyframeResolver=new d(r,(t,e,n)=>this.onKeyframesResolved(t,e,c,!n),a,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,i){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:h}=n;this.resolvedAt=ft.now(),function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=Dn(s,e),a=Dn(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||An(n))&&i)}(t,s,o,r)||(!v.instantAnimations&&a||h?.(We(t,n,e)),t[0]=t[t.length-1],kn(n),n.repeat=0);const u={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},c=!l&&function(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t,a=e?.owner?.current;if(!(a instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:h}=e.owner.getProps();return Ln()&&n&&Rn.has(n)&&("transform"!==n||!h)&&!l&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}(u)?new Cn({...u,element:u.motionValue.owner.current}):new ze(u);c.finished.then(()=>this.notifyFinished()).catch(S),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),pn=!0,fn(),mn(),pn=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const Bn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Fn(t,e,n=1){const[i,s]=function(t){const e=Bn.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return x(t)?parseFloat(t):t}return xt(s)?Fn(s,e,n+1):s}function On(t,e){return t?.[e]??t?.default??t}const In=new Set(["width","height","top","left","right","bottom",...sn]),Un=t=>e=>e.test(t),Nn=[wt,Bt,jt,Lt,Ot,Ft,{test:t=>"auto"===t,parse:t=>t}],Wn=t=>Nn.find(Un(t));function $n(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||w(t))}const Yn=new Set(["brightness","contrast","saturate","opacity"]);function Xn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(At)||[];if(!i)return t;const s=n.replace(i,"");let o=Yn.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Kn=/\b([a-z-]*)\(.*?\)/gu,zn={...qt,getAnimatableNone:t=>{const e=t.match(Kn);return e?e.map(Xn).join(" "):t}},Hn={...wt,transform:Math.round},Gn={borderWidth:Bt,borderTopWidth:Bt,borderRightWidth:Bt,borderBottomWidth:Bt,borderLeftWidth:Bt,borderRadius:Bt,radius:Bt,borderTopLeftRadius:Bt,borderTopRightRadius:Bt,borderBottomRightRadius:Bt,borderBottomLeftRadius:Bt,width:Bt,maxWidth:Bt,height:Bt,maxHeight:Bt,top:Bt,right:Bt,bottom:Bt,left:Bt,padding:Bt,paddingTop:Bt,paddingRight:Bt,paddingBottom:Bt,paddingLeft:Bt,margin:Bt,marginTop:Bt,marginRight:Bt,marginBottom:Bt,marginLeft:Bt,backgroundPositionX:Bt,backgroundPositionY:Bt,...{rotate:Lt,rotateX:Lt,rotateY:Lt,rotateZ:Lt,scale:St,scaleX:St,scaleY:St,scaleZ:St,skew:Lt,skewX:Lt,skewY:Lt,distance:Bt,translateX:Bt,translateY:Bt,translateZ:Bt,x:Bt,y:Bt,z:Bt,perspective:Bt,transformPerspective:Bt,opacity:Pt,originX:It,originY:It,originZ:Bt},zIndex:Hn,fillOpacity:Pt,strokeOpacity:Pt,numOctaves:Hn},qn={...Gn,color:Nt,backgroundColor:Nt,outlineColor:Nt,fill:Nt,stroke:Nt,borderColor:Nt,borderTopColor:Nt,borderRightColor:Nt,borderBottomColor:Nt,borderLeftColor:Nt,filter:zn,WebkitFilter:zn},Zn=t=>qn[t];function _n(t,e){let n=Zn(t);return n!==zn&&(n=qt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Jn=new Set(["auto","none","0"]);class Qn extends yn{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&(i=i.trim(),xt(i))){const s=Fn(i,e.current);void 0!==s&&(t[n]=s),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!In.has(n)||2!==t.length)return;const[i,s]=t,o=Wn(i),r=Wn(s);if(o!==r)if(rn(o)&&rn(r))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else hn[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||$n(t[e]))&&n.push(e);n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!Jn.has(e)&&Kt(e).values.length&&(i=t[s]),s++}if(i&&n)for(const s of e)t[s]=_n(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=hn[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);const s=n.length-1,o=n[s];n[s]=hn[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}const ti=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class ei{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{const e=ft.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=ft.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new V);const n=this.events[t].add(e);return"change"===t?()=>{n(),ht.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=ft.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return D(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ni(t,e){return new ei(t,e)}const{schedule:ii,cancel:si}=lt(queueMicrotask,!1),oi={x:!1,y:!1};function ri(){return oi.x||oi.y}function ai(t,e){const n=function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let i=document;e&&(i=e.current);const s=n?.[t]??i.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function li(t){return!("touch"===t.pointerType||ri())}const hi=(t,e)=>!!e&&(t===e||hi(t,e.parentElement)),ui=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,ci=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const di=new WeakSet;function pi(t){return e=>{"Enter"===e.key&&t(e)}}function mi(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function fi(t){return ui(t)&&!ri()}function yi(t,e,n={}){const[i,s,o]=ai(t,n),r=t=>{const i=t.currentTarget;if(!fi(t))return;di.add(i);const o=e(i,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),di.has(i)&&di.delete(i),fi(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,i===window||i===document||n.useGlobalTarget||hi(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach(t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),T(e=t)&&"offsetHeight"in e&&(t.addEventListener("focus",t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=pi(()=>{if(di.has(n))return;mi(n,"down");const t=pi(()=>{mi(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>mi(n,"cancel"),e)});n.addEventListener("keydown",i,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),e)})(t,s)),function(t){return ci.has(t.tagName)||-1!==t.tabIndex}(t)||t.hasAttribute("tabindex")||(t.tabIndex=0))}),o}function gi(t){return T(t)&&"ownerSVGElement"in t}const vi=t=>Boolean(t&&t.getVelocity),xi=[...Nn,Nt,qt],Ti={};function wi(t,{layout:e,layoutId:n}){return on.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Ti[t]||"opacity"===t)}const Pi={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Si=sn.length;function bi(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const t in e){const n=e[t];if(on.has(t))r=!0;else if(gt(t))s[t]=n;else{const e=ti(n,Gn[t]);t.startsWith("origin")?(a=!0,o[t]=e):i[t]=e}}if(e.transform||(r||n?i.transform=function(t,e,n){let i="",s=!0;for(let o=0;o<Si;o++){const r=sn[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=ti(a,Gn[r]);l||(s=!1,i+=`${Pi[r]||r}(${t}) `),n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}const Ai=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Ei(t,e,n){for(const i in e)vi(e[i])||wi(i,n)||(t[i]=e[i])}function Vi(t,e){const n={};return Ei(n,t.style||{},t),Object.assign(n,function({transformTemplate:t},e){return s(()=>{const n={style:{},transform:{},transformOrigin:{},vars:{}};return bi(n,e,t),Object.assign({},n.vars,n.style)},[e])}(t,e)),n}function Mi(t,e){const n={},i=Vi(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const Ci={offset:"stroke-dashoffset",array:"stroke-dasharray"},Di={offset:"strokeDashoffset",array:"strokeDasharray"};function ki(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,h,u){if(bi(t,a,h),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=u?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==n&&(c.y=n),void 0!==i&&(c.scale=i),void 0!==s&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Ci:Di;t[o.offset]=Bt.transform(-i);const r=Bt.transform(e),a=Bt.transform(n);t[o.array]=`${r} ${a}`}(c,s,o,r,!1)}const Ri=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Li=t=>"string"==typeof t&&"svg"===t.toLowerCase();function ji(t,e,n,i){const o=s(()=>{const n={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return ki(n,e,Li(i),t.transformTemplate,t.style),{...n.attrs,style:{...n.style}}},[e]);if(t.style){const e={};Ei(e,t.style,t),o.style={...e,...o.style}}return o}const Bi=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Fi(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Bi.has(t)}let Oi=t=>!Fi(t);try{"function"==typeof(Ii=require("@emotion/is-prop-valid").default)&&(Oi=t=>t.startsWith("on")?!Fi(t):Ii(t))}catch{}var Ii;const Ui=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ni(t){return"string"==typeof t&&!t.includes("-")&&!!(Ui.indexOf(t)>-1||/[A-Z]/u.test(t))}function Wi(t,e,n,{latestValues:i},a,l=!1){const h=(Ni(t)?ji:Mi)(e,i,a,t),u=function(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(Oi(s)||!0===n&&Fi(s)||!e&&!Fi(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}(e,"string"==typeof t,l),c=t!==o?{...u,...h,ref:n}:{},{children:d}=e,p=s(()=>vi(d)?d.get():d,[d]);return r(t,{...c,children:p})}const $i=n(null);function Yi(t){const e=[{},{}];return t?.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function Xi(t,e,n,i){if("function"==typeof e){const[s,o]=Yi(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=Yi(i);e=e(void 0!==n?n:t.custom,s,o)}return e}function Ki(t){return vi(t)?t.get():t}function zi(t,e,n,i){const s={},o=i(t,{});for(const t in o)s[t]=Ki(o[t]);let{initial:r,animate:a}=t;const l=nt(t),h=it(t);e&&h&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let u=!!n&&!1===n.initial;u=u||!1===r;const c=u?a:r;if(c&&"boolean"!=typeof c&&!J(c)){const e=Array.isArray(c)?c:[c];for(let n=0;n<e.length;n++){const i=Xi(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const t in n){let e=n[t];if(Array.isArray(e)){e=e[u?e.length-1:0]}null!==e&&(s[t]=e)}for(const e in t)s[e]=t[e]}}}return s}const Hi=t=>(e,n)=>{const s=i(_),o=i($i),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:zi(n,i,s,t),renderState:e()}}(t,e,s,o);return n?r():function(t){const e=a(null);return null===e.current&&(e.current=t()),e.current}(r)};function Gi(t,e,n){const{style:i}=t,s={};for(const o in i)(vi(i[o])||e.style&&vi(e.style[o])||wi(o,t)||void 0!==n?.getValue(o)?.liveStyle)&&(s[o]=i[o]);return s}const qi=Hi({scrapeMotionValuesFromProps:Gi,createRenderState:Ai});function Zi(t,e,n){const i=Gi(t,e,n);for(const n in t)if(vi(t[n])||vi(e[n])){i[-1!==sn.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]}return i}const _i=Hi({scrapeMotionValuesFromProps:Zi,createRenderState:Ri}),Ji="undefined"!=typeof window,Qi={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ts={};for(const t in Qi)ts[t]={isEnabled:e=>Qi[t].some(t=>!!e[t])};const es=Symbol.for("motionComponentSymbol");function ns(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function is(t,e,n){return l(i=>{i&&t.onMount&&t.onMount(i),e&&(i?e.mount(i):e.unmount()),n&&("function"==typeof n?n(i):ns(n)&&(n.current=i))},[e])}const ss=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),os="data-"+ss("framerAppearId"),rs=n({}),as=Ji?h:u;function ls(t,e,n,s,o){const{visualElement:r}=i(_),l=i(q),h=i($i),d=i(Z).reducedMotion,p=a(null);s=s||l.renderer,!p.current&&s&&(p.current=s(t,{visualState:e,parent:r,props:n,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:d}));const m=p.current,f=i(rs);!m||m.projection||!o||"html"!==m.type&&"svg"!==m.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:hs(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&ns(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,crossfade:u,layoutScroll:l,layoutRoot:h})}(p.current,n,o,f);const y=a(!1);c(()=>{m&&y.current&&m.update(n,h)});const g=n[os],v=a(Boolean(g)&&!window.MotionHandoffIsComplete?.(g)&&window.MotionHasOptimisedAnimation?.(g));return as(()=>{m&&(y.current=!0,window.MotionIsMounted=!0,m.updateFeatures(),m.scheduleRenderMicrotask(),v.current&&m.animationState&&m.animationState.animateChanges())}),u(()=>{m&&(!v.current&&m.animationState&&m.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(g)}),v.current=!1),m.enteringChildren=void 0)}),m}function hs(t){if(t)return!1!==t.options.allowProjection?t.projection:hs(t.parent)}function us(n,{forwardMotionProps:s=!1}={},o,r){o&&function(t){for(const e in t)ts[e]={...ts[e],...t[e]}}(o);const a=Ni(n)?_i:qi;function l(o,l){let h;const u={...i(Z),...o,layoutId:cs(o)},{isStatic:c}=u,d=st(o),p=a(o,c);if(!c&&Ji){i(q).strict;const t=function(t){const{drag:e,layout:n}=ts;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(u);h=t.MeasureLayout,d.visualElement=ls(n,p,u,r,t.ProjectionNode)}return t(_.Provider,{value:d,children:[h&&d.visualElement?e(h,{visualElement:d.visualElement,...u}):null,Wi(n,o,is(p,d.visualElement,l),p,c,s)]})}l.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;const h=d(l);return h[es]=n,h}function cs({layoutId:t}){const e=i(G).id;return e&&void 0!==t?e+"-"+t:t}function ds({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function ps(t){return void 0===t||1===t}function ms({scale:t,scaleX:e,scaleY:n}){return!ps(t)||!ps(e)||!ps(n)}function fs(t){return ms(t)||ys(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function ys(t){return gs(t.x)||gs(t.y)}function gs(t){return t&&"0%"!==t}function vs(t,e,n){return n+e*(t-n)}function xs(t,e,n,i,s){return void 0!==s&&(t=vs(t,s,i)),vs(t,n,i)+e}function Ts(t,e=0,n=1,i,s){t.min=xs(t.min,e,n,i,s),t.max=xs(t.max,e,n,i,s)}function ws(t,{x:e,y:n}){Ts(t.x,e.translate,e.scale,e.originPoint),Ts(t.y,n.translate,n.scale,n.originPoint)}const Ps=.999999999999,Ss=1.0000000000001;function bs(t,e){t.min=t.min+e,t.max=t.max+e}function As(t,e,n,i,s=.5){Ts(t,e,n,Jt(t.min,t.max,s),i)}function Es(t,e){As(t.x,e.x,e.scaleX,e.scale,e.originX),As(t.y,e.y,e.scaleY,e.scale,e.originY)}function Vs(t,e){return ds(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const Ms=()=>({x:{min:0,max:0},y:{min:0,max:0}}),Cs={current:null},Ds={current:!1};const ks=new WeakMap;const Rs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Ls{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=yn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=ft.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,ht.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=nt(e),this.isVariantNode=it(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in u){const e=u[t];void 0!==a[t]&&vi(e)&&e.set(a[t])}}mount(t){this.current=t,ks.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),Ds.current||function(){if(Ds.current=!0,Ji)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Cs.current=t.matches;t.addEventListener("change",e),e()}else Cs.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Cs.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),ut(this.notifyUpdate),ut(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=on.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&ht.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{i(),s&&s(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in ts){const e=ts[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<Rs.length;e++){const n=Rs[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const i=t["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(vi(s))t.addValue(i,s);else if(vi(o))t.addValue(i,ni(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,ni(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=ni(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var i;return null!=n&&("string"==typeof n&&(x(n)||w(n))?n=parseFloat(n):(i=n,!xi.find(Un(i))&&qt.test(e)&&(n=_n(t,e))),this.setBaseTarget(t,vi(n)?n.get():n)),vi(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const i=Xi(this.props,e,this.presenceContext?.custom);i&&(n=i[t])}if(e&&void 0!==n)return n;const i=this.getBaseTargetFromProps(this.props,t);return void 0===i||vi(i)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new V),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){ii.render(this.render)}}class js extends Ls{constructor(){super(...arguments),this.KeyframeResolver=Qn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;vi(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function Bs(t,{style:e,vars:n},i,s){const o=t.style;let r;for(r in e)o[r]=e[r];for(r in s?.applyProjectionStyles(o,i),n)o.setProperty(r,n[r])}class Fs extends js{constructor(){super(...arguments),this.type="html",this.renderInstance=Bs}readValueFromInstance(t,e){if(on.has(e))return this.projection?.isProjecting?tn(e):((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return en(n,e)})(t,e);{const i=(n=t,window.getComputedStyle(n)),s=(gt(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return Vs(t,e)}build(t,e,n){bi(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return Gi(t,e,n)}}const Os=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class Is extends js{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Ms}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(on.has(e)){const t=Zn(e);return t&&t.default||0}return e=Os.has(e)?e:ss(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return Zi(t,e,n)}build(t,e,n){ki(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,i){!function(t,e,n,i){Bs(t,e,void 0,i);for(const n in e.attrs)t.setAttribute(Os.has(n)?n:ss(n),e.attrs[n])}(t,e,0,i)}mount(t){this.isSVGTag=Li(t.tagName),super.mount(t)}}const Us=(t,e)=>Ni(t)?new Is(e):new Fs(e,{allowProjection:t!==o});function Ns(t,e,n){const i=t.getProps();return Xi(i,e,void 0!==n?n:i.custom,t)}const Ws=t=>Array.isArray(t);function $s(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ni(n))}function Ys(t){return Ws(t)?t[t.length-1]||0:t}function Xs(t,e){const n=t.getValue("willChange");if(i=n,Boolean(vi(i)&&i.add))return n.add(e);if(!n&&v.WillChange){const n=new v.WillChange("auto");t.addValue("willChange",n),n.add(e)}var i}function Ks(t){return t.props[os]}const zs=t=>null!==t;const Hs={type:"spring",stiffness:500,damping:25,restSpeed:10},Gs={type:"keyframes",duration:.8},qs={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Zs=(t,{keyframes:e})=>e.length>2?Gs:on.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Hs:qs;const _s=(t,e,n,i={},s,o)=>r=>{const a=On(i,t)||{},l=a.delay||i.delay||0;let{elapsed:h=0}=i;h-=M(l);const u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-h,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:h,...u}){return!!Object.keys(u).length})(a)||Object.assign(u,Zs(t,u)),u.duration&&(u.duration=M(u.duration)),u.repeatDelay&&(u.repeatDelay=M(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let c=!1;if((!1===u.type||0===u.duration&&!u.repeatDelay)&&(kn(u),0===u.delay&&(c=!0)),(v.instantAnimations||v.skipAnimations)&&(c=!0,kn(u),u.delay=0),u.allowFlatten=!a.type&&!a.ease,c&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(zs),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}(u.keyframes,a);if(void 0!==t)return void ht.update(()=>{u.onUpdate(t),u.onComplete()})}return a.isSync?new ze(u):new jn(u)};function Js({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function Qs(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],h=s&&t.animationState&&t.animationState.getState()[s];for(const e in a){const i=t.getValue(e,t.latestValues[e]??null),s=a[e];if(void 0===s||h&&Js(h,e))continue;const r={delay:n,...On(o||{},e)},u=i.get();if(void 0!==u&&!i.isAnimating&&!Array.isArray(s)&&s===u&&!r.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const n=Ks(t);if(n){const t=window.MotionHandoffAnimation(n,e,ht);null!==t&&(r.startTime=t,c=!0)}}Xs(t,e),i.start(_s(e,i,s,t.shouldReduceMotion&&In.has(e)?{type:!1}:r,t,c));const d=i.animation;d&&l.push(d)}return r&&Promise.all(l).then(()=>{ht.update(()=>{r&&function(t,e){const n=Ns(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const e in o)$s(t,e,Ys(o[e]))}(t,r)})}),l}function to(t,e,n,i=0,s=1){const o=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),r=t.size,a=(r-1)*i;return"function"==typeof n?n(o,r):1===s?o*i:a-o*i}function eo(t,e,n={}){const i=Ns(t,e,"exit"===n.type?t.presenceContext?.custom:void 0);let{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(Qs(t,i,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=s;return function(t,e,n=0,i=0,s=0,o=1,r){const a=[];for(const l of t.variantChildren)l.notify("AnimationStart",e),a.push(eo(l,e,{...r,delay:n+("function"==typeof i?0:i)+to(t.variantChildren,l,i,s,o)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(a)}(t,e,i,o,r,a,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then(()=>e())}return Promise.all([o(),r(n.delay)])}function no(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}const io=et.length;function so(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&so(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<io;n++){const i=et[n],s=t.props[i];(Q(s)||!1===s)&&(e[i]=s)}return e}const oo=[...tt].reverse(),ro=tt.length;function ao(t){return e=>Promise.all(e.map(({animation:e,options:n})=>function(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map(e=>eo(t,e,n));i=Promise.all(s)}else if("string"==typeof e)i=eo(t,e,n);else{const s="function"==typeof e?Ns(t,e,n.custom):e;i=Promise.all(Qs(t,s,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}(t,e,n)))}function lo(t){let e=ao(t),n=co(),i=!0;const s=e=>(n,i)=>{const s=Ns(t,i,"exit"===e?t.presenceContext?.custom:void 0);if(s){const{transition:t,transitionEnd:e,...i}=s;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=so(t.parent)||{},l=[],h=new Set;let u={},c=1/0;for(let e=0;e<ro;e++){const d=oo[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=Q(m),y=d===o?p.isActive:null;!1===y&&(c=e);let g=m===a[d]&&m!==r[d]&&f;if(g&&i&&t.manuallyAnimateOnMount&&(g=!1),p.protectedKeys={...u},!p.isActive&&null===y||!m&&!p.prevProp||J(m)||"boolean"==typeof m)continue;const v=ho(p.prevProp,m);let x=v||d===o&&p.isActive&&!g&&f||e>c&&f,T=!1;const w=Array.isArray(m)?m:[m];let P=w.reduce(s(d),{});!1===y&&(P={});const{prevResolvedValues:S={}}=p,b={...S,...P},A=e=>{x=!0,h.has(e)&&(T=!0,h.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in b){const e=P[t],n=S[t];if(u.hasOwnProperty(t))continue;let i=!1;i=Ws(e)&&Ws(n)?!no(e,n):e!==n,i?null!=e?A(t):h.add(t):void 0!==e&&h.has(t)?A(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=P,p.isActive&&(u={...u,...P}),i&&t.blockInitialAnimation&&(x=!1);const E=g&&v;x&&(!E||T)&&l.push(...w.map(e=>{const n={type:d};if("string"==typeof e&&i&&!E&&t.manuallyAnimateOnMount&&t.parent){const{parent:i}=t,s=Ns(i,e);if(i.enteringChildren&&s){const{delayChildren:e}=s.transition||{};n.delay=to(i.enteringChildren,t,e)}}return{animation:e,options:n}}))}if(h.size){const e={};if("boolean"!=typeof r.initial){const n=Ns(t,Array.isArray(r.initial)?r.initial[0]:r.initial);n&&n.transition&&(e.transition=n.transition)}h.forEach(n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=i??null}),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){if(n[e].isActive===i)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,i)),n[e].isActive=i;const s=o(e);for(const t in n)n[t].protectedKeys={};return s},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=co(),i=!0}}}function ho(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!no(e,t)}function uo(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function co(){return{animate:uo(!0),whileInView:uo(),whileHover:uo(),whileTap:uo(),whileDrag:uo(),whileFocus:uo(),exit:uo()}}class po{constructor(t){this.isMounted=!1,this.node=t}update(){}}let mo=0;const fo={animation:{Feature:class extends po{constructor(t){super(t),t.animationState||(t.animationState=lo(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();J(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends po{constructor(){super(...arguments),this.id=mo++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function yo(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function go(t){return{point:{x:t.pageX,y:t.pageY}}}function vo(t,e,n,i){return yo(t,e,(t=>e=>ui(e)&&t(e,go(e)))(n),i)}function xo(t){return t.max-t.min}function To(t,e,n,i=.5){t.origin=i,t.originPoint=Jt(e.min,e.max,t.origin),t.scale=xo(n)/xo(e),t.translate=Jt(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function wo(t,e,n,i){To(t.x,e.x,n.x,i?i.originX:void 0),To(t.y,e.y,n.y,i?i.originY:void 0)}function Po(t,e,n){t.min=n.min+e.min,t.max=t.min+xo(e)}function So(t,e,n){t.min=e.min-n.min,t.max=t.min+xo(e)}function bo(t,e,n){So(t.x,e.x,n.x),So(t.y,e.y,n.y)}function Ao(t){return[t("x"),t("y")]}const Eo=({current:t})=>t?t.ownerDocument.defaultView:null,Vo=(t,e)=>Math.abs(t-e);class Mo{constructor(t,e,{transformPagePoint:n,contextWindow:i=window,dragSnapToOrigin:s=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=ko(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){const n=Vo(t.x,e.x),i=Vo(t.y,e.y);return Math.sqrt(n**2+i**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=ct;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Co(e,this.transformPagePoint),ht.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=ko("pointercancel"===t.type?this.lastMoveEventInfo:Co(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!ui(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.distanceThreshold=o,this.contextWindow=i||window;const r=Co(go(t),this.transformPagePoint),{point:a}=r,{timestamp:l}=ct;this.history=[{...a,timestamp:l}];const{onSessionStart:h}=e;h&&h(t,ko(r,this.history)),this.removeListeners=A(vo(this.contextWindow,"pointermove",this.handlePointerMove),vo(this.contextWindow,"pointerup",this.handlePointerUp),vo(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),ut(this.updatePoint)}}function Co(t,e){return e?{point:e(t.point)}:t}function Do(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ko({point:t},e){return{point:t,delta:Do(t,Lo(e)),offset:Do(t,Ro(e)),velocity:jo(e,.1)}}function Ro(t){return t[0]}function Lo(t){return t[t.length-1]}function jo(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=Lo(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>M(e)));)n--;if(!i)return{x:0,y:0};const o=C(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Bo(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function Fo(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const Oo=.35;function Io(t,e,n){return{min:Uo(t,e),max:Uo(t,n)}}function Uo(t,e){return"number"==typeof t?t:t[e]||0}const No=new WeakMap;class Wo{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:n}={}){const{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;const{dragSnapToOrigin:s}=this.getProps();this.panSession=new Mo(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(go(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(o=n)||"y"===o?oi[o]?null:(oi[o]=!0,()=>{oi[o]=!1}):oi.x||oi.y?null:(oi.x=oi.y=!0,()=>{oi.x=oi.y=!1}),!this.openDragLock))return;var o;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ao(t=>{let e=this.getAxisMotionValue(t).get()||0;if(jt.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=xo(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),s&&ht.postRender(()=>s(t,e)),Xs(this.visualElement,"transform");const{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>Ao(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,distanceThreshold:n,contextWindow:Eo(this.visualElement)})}stop(t,e){const n=t||this.latestPointerEvent,i=e||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!i||!n)return;const{velocity:o}=i;this.startAnimation(o);const{onDragEnd:r}=this.getProps();r&&ht.postRender(()=>r(n,i))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!$o(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?Jt(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?Jt(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&ns(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!n)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:Bo(t.x,n,s),y:Bo(t.y,e,i)}}(n.layoutBox,t),this.elastic=function(t=Oo){return!1===t?t=0:!0===t&&(t=Oo),{x:Io(t,"left","right"),y:Io(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&Ao(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!ns(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=Vs(t,n),{scroll:s}=e;return s&&(bs(i.x,s.offset.x),bs(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:Fo(t.x,e.x),y:Fo(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=ds(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=Ao(r=>{if(!$o(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const h=i?200:1e6,u=i?40:1e7,c={type:"inertia",velocity:n?t[r]:0,bounceStiffness:h,bounceDamping:u,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,c)});return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return Xs(this.visualElement,t),n.start(_s(t,n,0,e,this.visualElement,!1))}stopAnimation(){Ao(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Ao(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){Ao(e=>{const{drag:n}=this.getProps();if(!$o(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-Jt(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!ns(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Ao(t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=xo(t),s=xo(e);return s>i?n=E(e.min,e.max-i,t.min):i>s&&(n=E(t.min,t.max-s,e.min)),g(0,1,n)}({min:n,max:n},this.constraints[t])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Ao(e=>{if(!$o(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(Jt(s,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;No.set(this.visualElement,this);const t=vo(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),e=()=>{const{dragConstraints:t}=this.getProps();ns(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),ht.read(e);const s=yo(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Ao(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Oo,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function $o(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const Yo=t=>(e,n)=>{t&&ht.postRender(()=>t(e,n))};const Xo=(t,e)=>t.depth-e.depth;class Ko{constructor(){this.children=[],this.isDirty=!1}add(t){f(this.children,t),this.isDirty=!0}remove(t){y(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Xo),this.isDirty=!1,this.children.forEach(t)}}const zo=["TopLeft","TopRight","BottomLeft","BottomRight"],Ho=zo.length,Go=t=>"string"==typeof t?parseFloat(t):t,qo=t=>"number"==typeof t||Bt.test(t);function Zo(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const _o=Qo(0,.5,N),Jo=Qo(.5,.95,S);function Qo(t,e,n){return i=>i<t?0:i>e?1:n(E(t,e,i))}function tr(t,e){t.min=e.min,t.max=e.max}function er(t,e){tr(t.x,e.x),tr(t.y,e.y)}function nr(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ir(t,e,n,i,s){return t=vs(t-=e,1/n,i),void 0!==s&&(t=vs(t,1/s,i)),t}function sr(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){jt.test(e)&&(e=parseFloat(e),e=Jt(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=Jt(o.min,o.max,i);t===o&&(a-=e),t.min=ir(t.min,e,n,a,s),t.max=ir(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const or=["x","scaleX","originX"],rr=["y","scaleY","originY"];function ar(t,e,n,i){sr(t.x,e,or,n?n.x:void 0,i?i.x:void 0),sr(t.y,e,rr,n?n.y:void 0,i?i.y:void 0)}function lr(t){return 0===t.translate&&1===t.scale}function hr(t){return lr(t.x)&&lr(t.y)}function ur(t,e){return t.min===e.min&&t.max===e.max}function cr(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function dr(t,e){return cr(t.x,e.x)&&cr(t.y,e.y)}function pr(t){return xo(t.x)/xo(t.y)}function mr(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class fr{constructor(){this.members=[]}add(t){f(this.members,t),t.scheduleRender()}remove(t){if(y(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let n;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const yr={hasAnimatedSinceResize:!0,hasEverUpdated:!1},gr=["","X","Y","Z"];let vr=0;function xr(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function Tr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=Ks(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",ht,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&Tr(i)}function wr({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=e?.()){this.id=vr++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(br),this.nodes.forEach(kr),this.nodes.forEach(Rr),this.nodes.forEach(Ar)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ko)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new V),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;var n;this.isSVG=gi(e)&&!(gi(n=e)&&"svg"===n.tagName),this.instance=e;const{layoutId:i,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let n,i=0;const s=()=>this.root.updateBlockedByResize=!1;ht.read(()=>{i=window.innerWidth}),t(e,()=>{const t=window.innerWidth;t!==i&&(i=t,this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){const n=ft.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(ut(i),t(o-e))};return ht.setup(i,!0),()=>ut(i)}(s,250),yr.hasAnimatedSinceResize&&(yr.hasAnimatedSinceResize=!1,this.nodes.forEach(Dr)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||Ir,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!dr(this.targetLayout,i),h=!e&&n;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const e={...On(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||Dr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ut(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Lr),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Tr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Vr);if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(Mr);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(Cr),this.nodes.forEach(Pr),this.nodes.forEach(Sr)):this.nodes.forEach(Mr),this.clearAllSnapshots();const t=ft.now();ct.delta=g(0,1e3/60,t-ct.timestamp),ct.timestamp=t,ct.isProcessing=!0,dt.update.process(ct),dt.preRender.process(ct),dt.render.process(ct),ct.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ii.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Er),this.sharedNodes.forEach(jr)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ht.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ht.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||xo(this.snapshot.measuredBox.x)||xo(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!hr(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||fs(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),Wr((i=n).x),Wr(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Yr))){const{scroll:t}=this.root;t&&(bs(e.x,t.offset.x),bs(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(er(e,t),this.scroll?.wasRoot)return e;for(let n=0;n<this.path.length;n++){const i=this.path[n],{scroll:s,options:o}=i;i!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&er(e,t),bs(e.x,s.offset.x),bs(e.y,s.offset.y))}return e}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};er(n,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&Es(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),fs(i.latestValues)&&Es(n,i.latestValues)}return fs(this.latestValues)&&Es(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};er(e,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];if(!n.instance)continue;if(!fs(n.latestValues))continue;ms(n.latestValues)&&n.updateSnapshot();const i=Ms();er(i,n.measurePageBox()),ar(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return fs(this.latestValues)&&ar(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ct.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const n=Boolean(this.resumingFrom)||this!==e;if(!(t||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:i,layoutId:s}=this.options;if(this.layout&&(i||s)){if(this.resolvedRelativeTargetAt=ct.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},bo(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),er(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var o,r,a;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,r=this.relativeTarget,a=this.relativeParent.target,Po(o.x,r.x,a.x),Po(o.y,r.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):er(this.target,this.layout.layoutBox),ws(this.target,this.targetDelta)):er(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},bo(this.relativeTargetOrigin,this.target,t.target),er(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!ms(this.parent.latestValues)&&!ys(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===ct.timestamp&&(n=!1),n)return;const{layout:i,layoutId:s}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!s)return;er(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,r=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Es(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,ws(t,r)),i&&fs(o.latestValues)&&Es(t,o.latestValues))}e.x<Ss&&e.x>Ps&&(e.x=1),e.y<Ss&&e.y>Ps&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(nr(this.prevProjectionDelta.x,this.projectionDelta.x),nr(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),wo(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===r&&mr(this.projectionDelta.x,this.prevProjectionDelta.x)&&mr(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),h=!l||l.members.length<=1,u=Boolean(a&&!h&&!0===this.options.crossfade&&!this.path.some(Or));let c;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d,p,m,f,y;Br(o.x,t.x,n),Br(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(bo(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=r,y=n,Fr(p.x,m.x,f.x,y),Fr(p.y,m.y,f.y,y),c&&(l=this.relativeTarget,d=c,ur(l.x,d.x)&&ur(l.y,d.y))&&(this.isProjectionDirty=!1),c||(c={x:{min:0,max:0},y:{min:0,max:0}}),er(c,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=Jt(0,n.opacity??1,_o(i)),t.opacityExit=Jt(e.opacity??1,0,Jo(i))):o&&(t.opacity=Jt(e.opacity??1,n.opacity??1,i));for(let s=0;s<Ho;s++){const o=`border${zo[s]}Radius`;let r=Zo(e,o),a=Zo(n,o);void 0===r&&void 0===a||(r||(r=0),a||(a=0),0===r||0===a||qo(r)===qo(a)?(t[o]=Math.max(Jt(Go(r),Go(a),i),0),(jt.test(a)||jt.test(r))&&(t[o]+="%")):t[o]=a)}(e.rotate||n.rotate)&&(t.rotate=Jt(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,u,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ut(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ht.update(()=>{yr.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ni(0)),this.currentAnimation=function(t,e,n){const i=vi(t)?t:ni(t);return i.start(_s("",i,e,n)),i.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&$r(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=xo(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=xo(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}er(e,n),Es(e,s),wo(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new fr);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&xr("z",t,i,this.animationValues);for(let e=0;e<gr.length;e++)xr(`rotate${gr[e]}`,t,i,this.animationValues),xr(`skew${gr[e]}`,t,i,this.animationValues);t.render();for(const e in i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return void(t.visibility="hidden");const n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=Ki(e?.pointerEvents)||"",void(t.transform=n?n(this.latestValues,""):"none");const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target)return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=Ki(e?.pointerEvents)||""),void(this.hasProjected&&!fs(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1));t.visibility="";const s=i.animationValues||i.latestValues;this.applyTransformsToTarget();let o=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=n?.z||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,s);n&&(o=n(s,o)),t.transform=o;const{x:r,y:a}=this.projectionDelta;t.transformOrigin=`${100*r.origin}% ${100*a.origin}% 0`,i.animationValues?t.opacity=i===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:t.opacity=i===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const e in Ti){if(void 0===s[e])continue;const{correct:n,applyTo:r,isCSSVariable:a}=Ti[e],l="none"===o?s[e]:n(s[e],i);if(r){const e=r.length;for(let n=0;n<e;n++)t[r[n]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=i===this?Ki(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(Vr),this.root.sharedNodes.clear()}}}function Pr(t){t.updateLayout()}function Sr(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:i}=t.layout,{animationType:s}=t.options,o=e.source!==t.layout.source;"size"===s?Ao(t=>{const i=o?e.measuredBox[t]:e.layoutBox[t],s=xo(i);i.min=n[t].min,i.max=i.min+s}):$r(s,e.layoutBox,n)&&Ao(i=>{const s=o?e.measuredBox[i]:e.layoutBox[i],r=xo(n[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)});const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};wo(r,n,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?wo(a,t.applyTransform(i,!0),e.measuredBox):wo(a,n,e.layoutBox);const l=!hr(r);let h=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};bo(r,e.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};bo(a,n,o.layoutBox),dr(r,a)||(h=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function br(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Ar(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Er(t){t.clearSnapshot()}function Vr(t){t.clearMeasurements()}function Mr(t){t.isLayoutDirty=!1}function Cr(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Dr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function kr(t){t.resolveTargetDelta()}function Rr(t){t.calcProjection()}function Lr(t){t.resetSkewAndRotation()}function jr(t){t.removeLeadSnapshot()}function Br(t,e,n){t.translate=Jt(e.translate,0,n),t.scale=Jt(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Fr(t,e,n,i){t.min=Jt(e.min,n.min,i),t.max=Jt(e.max,n.max,i)}function Or(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Ir={duration:.45,ease:[.4,0,.1,1]},Ur=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Nr=Ur("applewebkit/")&&!Ur("chrome/")?Math.round:S;function Wr(t){t.min=Nr(t.min),t.max=Nr(t.max)}function $r(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=pr(e),s=pr(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function Yr(t){return t!==t.root&&t.scroll?.wasRoot}const Xr=wr({attachResizeListener:(t,e)=>yo(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Kr={current:void 0},zr=wr({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Kr.current){const t=new Xr({});t.mount(window),t.setOptions({layoutScroll:!0}),Kr.current=t}return Kr.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)});function Hr(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Gr={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Bt.test(t))return t;t=parseFloat(t)}return`${Hr(t,e.target.x)}% ${Hr(t,e.target.y)}%`}},qr={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=qt.parse(t);if(s.length>5)return i;const o=qt.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const h=Jt(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=h),"number"==typeof s[3+r]&&(s[3+r]/=h),o(s)}};let Zr=!1;class _r extends m{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;!function(t){for(const e in t)Ti[e]=t[e],gt(e)&&(Ti[e].isCSSVariable=!0)}(Qr),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),Zr&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),yr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,{projection:o}=n;return o?(o.isPresent=s,Zr=!0,i||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||ht.postRender(()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),ii.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;Zr=!0,i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Jr(t){const[n,s]=function(t=!0){const e=i($i);if(null===e)return[!0,null];const{isPresent:n,onExitComplete:s,register:o}=e,r=p();u(()=>{if(t)return o(r)},[t]);const a=l(()=>t&&s&&s(r),[r,s,t]);return!n&&s?[!1,a]:[!0]}(),o=i(G);return e(_r,{...t,layoutGroup:o,switchLayoutGroup:i(rs),isPresent:n,safeToRemove:s})}const Qr={borderRadius:{...Gr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Gr,borderTopRightRadius:Gr,borderBottomLeftRadius:Gr,borderBottomRightRadius:Gr,boxShadow:qr},ta={pan:{Feature:class extends po{constructor(){super(...arguments),this.removePointerDownListener=S}onPointerDown(t){this.session=new Mo(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Eo(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:Yo(t),onStart:Yo(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&ht.postRender(()=>i(t,e))}}}mount(){this.removePointerDownListener=vo(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends po{constructor(t){super(t),this.removeGroupControls=S,this.removeListeners=S,this.controls=new Wo(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||S}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:zr,MeasureLayout:Jr}};function ea(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&ht.postRender(()=>s(e,go(e)))}function na(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&ht.postRender(()=>s(e,go(e)))}const ia=new WeakMap,sa=new WeakMap,oa=t=>{const e=ia.get(t.target);e&&e(t)},ra=t=>{t.forEach(oa)};function aa(t,e,n){const i=function({root:t,...e}){const n=t||document;sa.has(n)||sa.set(n,{});const i=sa.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(ra,{root:t,...e})),i[s]}(e);return ia.set(t,n),i.observe(t),()=>{ia.delete(t),i.unobserve(t)}}const la={some:0,all:1};const ha={...fo,...{inView:{Feature:class extends po{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:la[i]};return aa(this.node.current,o,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends po{mount(){const{current:t}=this.node;t&&(this.unmount=yi(t,(t,e)=>(na(this.node,e,"Start"),(t,{success:e})=>na(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends po{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=A(yo(this.node.current,"focus",()=>this.onFocus()),yo(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends po{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[i,s,o]=ai(t,n),r=t=>{if(!li(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const o=t=>{li(t)&&(i(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,s)};return i.forEach(t=>{t.addEventListener("pointerenter",r,s)}),o}(t,(t,e)=>(ea(this.node,e,"Start"),t=>ea(this.node,t,"End"))))}unmount(){}}}},...ta,...{layout:{ProjectionNode:zr,MeasureLayout:Jr}}};function ua(t,e){return us(t,e,ha,Us)}const ca=ua("div");export{ca as MotionDiv};
