import React from 'react';
import { motion } from 'framer-motion';
import { useGameStore } from '../../stores/gameStore';
import './LevelSelectPage.css';

const LevelSelectPage = () => {
  const { 
    levels, 
    unlockedLevels, 
    setCurrentScene, 
    startGame 
  } = useGameStore();

  const handleBackToHome = () => {
    setCurrentScene('home');
  };

  const handleLevelSelect = (levelId) => {
    if (unlockedLevels.includes(levelId)) {
      startGame(levelId);
    }
  };

  const isLevelUnlocked = (levelId) => {
    return unlockedLevels.includes(levelId);
  };

  return (
    <div className="level-select-page">
      {/* 头部导航 */}
      <motion.div 
        className="header"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <motion.button
          className="back-button btn btn-secondary"
          onClick={handleBackToHome}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span>←</span>
          <span>返回</span>
        </motion.button>
        
        <h1 className="page-title">
          <span>📸</span>
          <span>猫咪相册</span>
        </h1>
        
        <div className="header-spacer"></div>
      </motion.div>

      {/* 关卡网格 */}
      <div className="levels-container">
        <motion.div 
          className="levels-grid"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          {levels.map((level, index) => {
            const unlocked = isLevelUnlocked(level.id);
            
            return (
              <motion.div
                key={level.id}
                className={`level-card ${unlocked ? 'unlocked' : 'locked'} ${level.completed ? 'completed' : ''}`}
                initial={{ scale: 0, rotate: -10 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ 
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 260,
                  damping: 20
                }}
                whileHover={unlocked ? { 
                  scale: 1.05,
                  rotate: 2,
                  boxShadow: "0 10px 30px rgba(0, 0, 0, 0.2)"
                } : {}}
                whileTap={unlocked ? { scale: 0.95 } : {}}
                onClick={() => handleLevelSelect(level.id)}
              >
                {/* 关卡图片/预览 */}
                <div className="level-image">
                  {unlocked ? (
                    <div className="level-preview">
                      <div className="scene-preview">
                        {level.name === '温馨卧室' && (
                          <div className="bedroom-preview">
                            <div className="bed"></div>
                            <div className="window"></div>
                            <div className="cat-hint">🐱</div>
                          </div>
                        )}
                        {level.name === '静谧花园' && (
                          <div className="garden-preview">
                            <div className="tree"></div>
                            <div className="flowers"></div>
                            <div className="cat-hint">🐱</div>
                          </div>
                        )}
                      </div>
                      
                      {level.completed && (
                        <motion.div 
                          className="completed-badge"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 0.5 }}
                        >
                          ✓
                        </motion.div>
                      )}
                    </div>
                  ) : (
                    <div className="locked-preview">
                      <motion.div 
                        className="lock-icon"
                        animate={{ 
                          scale: [1, 1.1, 1],
                          rotate: [0, 5, -5, 0]
                        }}
                        transition={{ 
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        🔒
                      </motion.div>
                      <div className="paw-prints">
                        <span>🐾</span>
                        <span>🐾</span>
                        <span>🐾</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* 关卡信息 */}
                <div className="level-info">
                  <h3 className="level-name">{level.name}</h3>
                  <p className="level-description">{level.description}</p>
                  
                  {unlocked && (
                    <div className="level-stats">
                      <div className="stat">
                        <span className="stat-icon">🐱</span>
                        <span className="stat-text">{level.totalCats}只猫</span>
                      </div>
                      <div className="stat">
                        <span className="stat-icon">⏰</span>
                        <span className="stat-text">{level.timeLimit}秒</span>
                      </div>
                      {level.bestTime && (
                        <div className="stat best-time">
                          <span className="stat-icon">🏆</span>
                          <span className="stat-text">{level.bestTime}秒</span>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {!unlocked && (
                    <div className="unlock-hint">
                      完成前一关解锁
                    </div>
                  )}
                </div>

                {/* 点击提示 */}
                {unlocked && (
                  <motion.div 
                    className="play-hint"
                    animate={{ opacity: [0.5, 1, 0.5] }}
                    transition={{ 
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    点击开始游戏
                  </motion.div>
                )}
              </motion.div>
            );
          })}
        </motion.div>

        {/* 首次进入提示 */}
        <motion.div 
          className="first-time-hint"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.6 }}
        >
          <motion.div 
            className="hint-arrow"
            animate={{ 
              y: [0, -10, 0],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{ 
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            ↑
          </motion.div>
          <p>点击关卡开始你的找猫之旅~</p>
        </motion.div>
      </div>
    </div>
  );
};

export default LevelSelectPage;
