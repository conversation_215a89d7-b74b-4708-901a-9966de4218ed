// 音效管理器
class SoundManager {
  constructor() {
    this.sounds = {};
    this.enabled = true;
    this.volume = 0.5;
    
    // 使用Web Audio API创建音效
    this.audioContext = null;
    this.initAudioContext();
    
    // 预定义音效
    this.soundDefinitions = {
      click: { frequency: 800, duration: 0.1, type: 'sine' },
      meow: { frequency: 400, duration: 0.3, type: 'sawtooth' },
      success: { frequency: 600, duration: 0.5, type: 'triangle' },
      fail: { frequency: 200, duration: 0.8, type: 'sawtooth' },
      hint: { frequency: 1000, duration: 0.2, type: 'sine' },
      tick: { frequency: 1200, duration: 0.05, type: 'square' },
      whoosh: { frequency: 300, duration: 0.3, type: 'sine' }
    };
  }

  initAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    } catch (error) {
      console.warn('Web Audio API not supported:', error);
    }
  }

  // 播放音效
  play(soundName, options = {}) {
    if (!this.enabled || !this.audioContext) return;

    const soundDef = this.soundDefinitions[soundName];
    if (!soundDef) {
      console.warn(`Sound "${soundName}" not found`);
      return;
    }

    try {
      this.playTone(soundDef, options);
    } catch (error) {
      console.warn('Error playing sound:', error);
    }
  }

  // 播放音调
  playTone(soundDef, options = {}) {
    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.frequency.setValueAtTime(
      options.frequency || soundDef.frequency, 
      this.audioContext.currentTime
    );
    oscillator.type = options.type || soundDef.type;

    const duration = options.duration || soundDef.duration;
    const volume = (options.volume || 1) * this.volume;

    gainNode.gain.setValueAtTime(volume, this.audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(
      0.01, 
      this.audioContext.currentTime + duration
    );

    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + duration);
  }

  // 播放猫叫声（更复杂的音效）
  playMeow() {
    if (!this.enabled || !this.audioContext) return;

    try {
      // 创建一个更真实的猫叫声
      const oscillator1 = this.audioContext.createOscillator();
      const oscillator2 = this.audioContext.createOscillator();
      const gainNode = this.audioContext.createGain();

      oscillator1.connect(gainNode);
      oscillator2.connect(gainNode);
      gainNode.connect(this.audioContext.destination);

      // 主频率
      oscillator1.frequency.setValueAtTime(400, this.audioContext.currentTime);
      oscillator1.frequency.exponentialRampToValueAtTime(200, this.audioContext.currentTime + 0.3);
      oscillator1.type = 'sawtooth';

      // 和声
      oscillator2.frequency.setValueAtTime(600, this.audioContext.currentTime);
      oscillator2.frequency.exponentialRampToValueAtTime(300, this.audioContext.currentTime + 0.3);
      oscillator2.type = 'triangle';

      gainNode.gain.setValueAtTime(this.volume * 0.3, this.audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);

      oscillator1.start(this.audioContext.currentTime);
      oscillator2.start(this.audioContext.currentTime);
      oscillator1.stop(this.audioContext.currentTime + 0.3);
      oscillator2.stop(this.audioContext.currentTime + 0.3);
    } catch (error) {
      console.warn('Error playing meow sound:', error);
    }
  }

  // 播放成功音效
  playSuccess() {
    if (!this.enabled || !this.audioContext) return;

    try {
      // 播放一系列上升的音调
      const frequencies = [523, 659, 784, 1047]; // C, E, G, C (大调和弦)
      
      frequencies.forEach((freq, index) => {
        setTimeout(() => {
          this.playTone({
            frequency: freq,
            duration: 0.2,
            type: 'sine'
          });
        }, index * 100);
      });
    } catch (error) {
      console.warn('Error playing success sound:', error);
    }
  }

  // 播放失败音效
  playFail() {
    if (!this.enabled || !this.audioContext) return;

    try {
      // 播放下降的音调
      const frequencies = [400, 350, 300, 250];
      
      frequencies.forEach((freq, index) => {
        setTimeout(() => {
          this.playTone({
            frequency: freq,
            duration: 0.15,
            type: 'sawtooth'
          });
        }, index * 150);
      });
    } catch (error) {
      console.warn('Error playing fail sound:', error);
    }
  }

  // 播放倒计时音效
  playTick() {
    this.play('tick', { volume: 0.3 });
  }

  // 播放点击音效
  playClick() {
    this.play('click', { volume: 0.2 });
  }

  // 播放提示音效
  playHint() {
    this.play('hint', { volume: 0.4 });
  }

  // 播放过渡音效
  playWhoosh() {
    this.play('whoosh', { volume: 0.3 });
  }

  // 设置音效开关
  setEnabled(enabled) {
    this.enabled = enabled;
  }

  // 设置音量
  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
  }

  // 获取音效状态
  isEnabled() {
    return this.enabled;
  }

  // 恢复音频上下文（用户交互后）
  resumeAudioContext() {
    if (this.audioContext && this.audioContext.state === 'suspended') {
      this.audioContext.resume();
    }
  }
}

// 创建全局音效管理器实例
export const soundManager = new SoundManager();

// 导出音效播放函数
export const playSound = (soundName, options) => {
  soundManager.play(soundName, options);
};

export const playMeow = () => {
  soundManager.playMeow();
};

export const playSuccess = () => {
  soundManager.playSuccess();
};

export const playFail = () => {
  soundManager.playFail();
};

export const playClick = () => {
  soundManager.playClick();
};

export const playHint = () => {
  soundManager.playHint();
};

export const playTick = () => {
  soundManager.playTick();
};

export const playWhoosh = () => {
  soundManager.playWhoosh();
};

// 初始化音效（需要用户交互）
export const initSounds = () => {
  soundManager.resumeAudioContext();
};
