import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useGameStore } from '../../stores/gameStore';
import './CollectionPage.css';

const CollectionPage = () => {
  const { 
    levels, 
    collectedCats, 
    achievements, 
    setCurrentScene 
  } = useGameStore();

  const [selectedTab, setSelectedTab] = useState('cats'); // 'cats' or 'achievements'
  const [selectedCat, setSelectedCat] = useState(null);

  const handleBackToHome = () => {
    setCurrentScene('home');
  };

  // 获取所有猫咪数据
  const getAllCats = () => {
    const allCats = [];
    levels.forEach(level => {
      level.cats.forEach(cat => {
        allCats.push({
          ...cat,
          levelName: level.name,
          levelId: level.id,
          unlocked: level.completed || collectedCats.some(c => c.id === cat.id)
        });
      });
    });
    return allCats;
  };

  const allCats = getAllCats();
  const unlockedCats = allCats.filter(cat => cat.unlocked);

  // 成就数据
  const achievementsList = [
    {
      id: 'first_cat',
      name: '初次相遇',
      description: '找到第一只猫咪',
      icon: '🐱',
      unlocked: unlockedCats.length > 0
    },
    {
      id: 'cat_lover',
      name: '猫语者',
      description: '找到5只猫咪',
      icon: '😻',
      unlocked: unlockedCats.length >= 5
    },
    {
      id: 'speed_finder',
      name: '闪电猎手',
      description: '在30秒内完成一关',
      icon: '⚡',
      unlocked: levels.some(level => level.bestTime && level.bestTime <= 30)
    },
    {
      id: 'no_hints',
      name: '独立探索者',
      description: '不使用提示完成一关',
      icon: '🔍',
      unlocked: achievements.includes('no_hints')
    },
    {
      id: 'all_levels',
      name: '猫咪大师',
      description: '完成所有关卡',
      icon: '👑',
      unlocked: levels.every(level => level.completed)
    }
  ];

  return (
    <div className="collection-page">
      {/* 头部导航 */}
      <motion.div 
        className="header"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <motion.button
          className="back-button btn btn-secondary"
          onClick={handleBackToHome}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span>←</span>
          <span>返回</span>
        </motion.button>
        
        <h1 className="page-title">
          <span>📚</span>
          <span>我的收藏</span>
        </h1>
        
        <div className="header-spacer"></div>
      </motion.div>

      {/* 标签切换 */}
      <motion.div 
        className="tab-switcher"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.6 }}
      >
        <motion.button
          className={`tab-button ${selectedTab === 'cats' ? 'active' : ''}`}
          onClick={() => setSelectedTab('cats')}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span>🐱</span>
          <span>猫咪图鉴</span>
          <span className="count">({unlockedCats.length}/{allCats.length})</span>
        </motion.button>
        
        <motion.button
          className={`tab-button ${selectedTab === 'achievements' ? 'active' : ''}`}
          onClick={() => setSelectedTab('achievements')}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span>🏆</span>
          <span>成就</span>
          <span className="count">({achievementsList.filter(a => a.unlocked).length}/{achievementsList.length})</span>
        </motion.button>
      </motion.div>

      {/* 内容区域 */}
      <div className="content-area">
        <AnimatePresence mode="wait">
          {selectedTab === 'cats' ? (
            <motion.div
              key="cats"
              className="cats-collection"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.4 }}
            >
              {/* 按关卡分组显示猫咪 */}
              {levels.map(level => {
                const levelCats = allCats.filter(cat => cat.levelId === level.id);
                const unlockedLevelCats = levelCats.filter(cat => cat.unlocked);
                
                return (
                  <motion.div
                    key={level.id}
                    className="level-section"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: level.id * 0.1 }}
                  >
                    <div className="level-header">
                      <h3>{level.name}</h3>
                      <span className="level-progress">
                        {unlockedLevelCats.length}/{levelCats.length}
                      </span>
                    </div>
                    
                    <div className="cats-grid">
                      {levelCats.map((cat, index) => (
                        <motion.div
                          key={cat.id}
                          className={`cat-card ${cat.unlocked ? 'unlocked' : 'locked'}`}
                          initial={{ scale: 0, rotate: -180 }}
                          animate={{ scale: 1, rotate: 0 }}
                          transition={{
                            delay: 0.5 + index * 0.1,
                            type: "spring",
                            stiffness: 260,
                            damping: 20
                          }}
                          whileHover={cat.unlocked ? { 
                            scale: 1.05,
                            boxShadow: "0 10px 30px rgba(0, 0, 0, 0.2)"
                          } : {}}
                          onClick={() => cat.unlocked && setSelectedCat(cat)}
                        >
                          <div className="cat-avatar">
                            {cat.unlocked ? '🐱' : '❓'}
                          </div>
                          <div className="cat-info">
                            <div className="cat-name">
                              {cat.unlocked ? cat.name : '???'}
                            </div>
                            {cat.unlocked && (
                              <div className="cat-description">
                                在{cat.levelName}中发现
                              </div>
                            )}
                          </div>
                          
                          {!cat.unlocked && (
                            <div className="lock-overlay">
                              <span>🔒</span>
                            </div>
                          )}
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                );
              })}
            </motion.div>
          ) : (
            <motion.div
              key="achievements"
              className="achievements-collection"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.4 }}
            >
              <div className="achievements-grid">
                {achievementsList.map((achievement, index) => (
                  <motion.div
                    key={achievement.id}
                    className={`achievement-card ${achievement.unlocked ? 'unlocked' : 'locked'}`}
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{
                      delay: index * 0.1,
                      type: "spring",
                      stiffness: 260,
                      damping: 20
                    }}
                    whileHover={achievement.unlocked ? { 
                      scale: 1.05,
                      boxShadow: "0 10px 30px rgba(0, 0, 0, 0.2)"
                    } : {}}
                  >
                    <div className="achievement-icon">
                      {achievement.unlocked ? achievement.icon : '🔒'}
                    </div>
                    <div className="achievement-info">
                      <div className="achievement-name">
                        {achievement.unlocked ? achievement.name : '未解锁'}
                      </div>
                      <div className="achievement-description">
                        {achievement.unlocked ? achievement.description : '继续游戏解锁更多成就'}
                      </div>
                    </div>
                    
                    {achievement.unlocked && (
                      <motion.div
                        className="unlock-badge"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.5 }}
                      >
                        ✓
                      </motion.div>
                    )}
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 猫咪详情弹窗 */}
      <AnimatePresence>
        {selectedCat && (
          <motion.div
            className="cat-detail-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedCat(null)}
          >
            <motion.div
              className="cat-detail-modal"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="modal-header">
                <h3>{selectedCat.name}</h3>
                <button 
                  className="close-button"
                  onClick={() => setSelectedCat(null)}
                >
                  ×
                </button>
              </div>
              
              <div className="modal-content">
                <div className="cat-large-avatar">🐱</div>
                <div className="cat-details">
                  <p><strong>发现地点:</strong> {selectedCat.levelName}</p>
                  <p><strong>隐藏位置:</strong> 在场景的神秘角落</p>
                  <p><strong>性格:</strong> 调皮可爱，喜欢躲猫猫</p>
                  <p><strong>特点:</strong> 每只猫咪都有独特的隐藏方式</p>
                </div>
              </div>
              
              <div className="modal-actions">
                <button 
                  className="btn btn-primary"
                  onClick={() => setSelectedCat(null)}
                >
                  关闭
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CollectionPage;
