import React from 'react';
import { useGameStore } from '../stores/gameStore';
import HomePage from './pages/HomePage';
import LevelSelectPage from './pages/LevelSelectPage';
import GamePage from './pages/GamePage';
import ResultPage from './pages/ResultPage';
import CollectionPage from './pages/CollectionPage';

const GameRouter = () => {
  const currentScene = useGameStore(state => state.currentScene);

  const renderCurrentScene = () => {
    switch (currentScene) {
      case 'home':
        return <HomePage />;
      case 'levelSelect':
        return <LevelSelectPage />;
      case 'game':
        return <GamePage />;
      case 'result':
        return <ResultPage />;
      case 'collection':
        return <CollectionPage />;
      default:
        return <HomePage />;
    }
  };

  return (
    <div className="game-container">
      {renderCurrentScene()}
    </div>
  );
};

export default GameRouter;
