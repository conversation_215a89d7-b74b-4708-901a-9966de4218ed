function t(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const e=(t,e,n)=>n>e?e:n<t?t:n;const n={},s=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);const i=t=>/^0[^.\s]+$/u.test(t);function r(t){let e;return()=>(void 0===e&&(e=t()),e)}const a=t=>t,o=(t,e)=>n=>e(t(n)),l=(...t)=>t.reduce(o),u=(t,e,n)=>{const s=e-t;return 0===s?1:(n-t)/s};class h{constructor(){this.subscriptions=[]}add(e){var n,s;return n=this.subscriptions,s=e,-1===n.indexOf(s)&&n.push(s),()=>t(this.subscriptions,e)}notify(t,e,n){const s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,n);else for(let i=0;i<s;i++){const s=this.subscriptions[i];s&&s(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const c=t=>1e3*t,d=t=>t/1e3;function p(t,e){return e?t*(1e3/e):0}const m=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function f(t,e,n,s){if(t===e&&n===s)return a;const i=e=>function(t,e,n,s,i){let r,a,o=0;do{a=e+(n-e)/2,r=m(a,s,i)-t,r>0?n=a:e=a}while(Math.abs(r)>1e-7&&++o<12);return a}(e,0,1,t,n);return t=>0===t||1===t?t:m(i(t),e,s)}const y=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,g=t=>e=>1-t(1-e),v=f(.33,1.53,.69,.99),b=g(v),w=y(b),T=t=>(t*=2)<1?.5*b(t):.5*(2-Math.pow(2,-10*(t-1))),M=t=>1-Math.sin(Math.acos(t)),V=g(M),A=y(M),S=f(.42,0,1,1),x=f(0,0,.58,1),k=f(.42,0,.58,1),C=t=>Array.isArray(t)&&"number"!=typeof t[0];function F(t,e){return C(t)?t[((t,e,n)=>{const s=e-t;return((n-t)%s+s)%s+t})(0,t.length,e)]:t}const P=t=>Array.isArray(t)&&"number"==typeof t[0],E={linear:a,easeIn:S,easeInOut:k,easeOut:x,circIn:M,circInOut:A,circOut:V,backIn:b,backInOut:w,backOut:v,anticipate:T},O=t=>{if(P(t)){t.length;const[e,n,s,i]=t;return f(e,n,s,i)}return"string"==typeof t?E[t]:t},D=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],I={value:null,addProjectionMetrics:null};function R(t,e){let s=!1,i=!0;const r={delta:0,timestamp:0,isProcessing:!1},a=()=>s=!0,o=D.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,s=new Set,i=!1,r=!1;const a=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){a.has(e)&&(h.schedule(e),t()),l++,e(o)}const h={schedule:(t,e=!1,r=!1)=>{const o=r&&i?n:s;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{s.delete(t),a.delete(t)},process:t=>{o=t,i?r=!0:(i=!0,[n,s]=[s,n],n.forEach(u),e&&I.value&&I.value.frameloop[e].push(l),l=0,n.clear(),i=!1,r&&(r=!1,h.process(t)))}};return h}(a,e?n:void 0),t),{}),{setup:l,read:u,resolveKeyframes:h,preUpdate:c,update:d,preRender:p,render:m,postRender:f}=o,y=()=>{const a=n.useManualTiming?r.timestamp:performance.now();s=!1,n.useManualTiming||(r.delta=i?1e3/60:Math.max(Math.min(a-r.timestamp,40),1)),r.timestamp=a,r.isProcessing=!0,l.process(r),u.process(r),h.process(r),c.process(r),d.process(r),p.process(r),m.process(r),f.process(r),r.isProcessing=!1,s&&e&&(i=!1,t(y))};return{schedule:D.reduce((e,n)=>{const a=o[n];return e[n]=(e,n=!1,o=!1)=>(s||(s=!0,i=!0,r.isProcessing||t(y)),a.schedule(e,n,o)),e},{}),cancel:t=>{for(let e=0;e<D.length;e++)o[D[e]].cancel(t)},state:r,steps:o}}const{schedule:B,cancel:N,state:K,steps:j}=R("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:a,!0);let W;function Y(){W=void 0}const $={now:()=>(void 0===W&&$.set(K.isProcessing||n.useManualTiming?K.timestamp:performance.now()),W),set:t=>{W=t,queueMicrotask(Y)}},L=t=>e=>"string"==typeof e&&e.startsWith(t),U=L("--"),X=L("var(--"),q=t=>!!X(t)&&z.test(t.split("/*")[0].trim()),z=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Z={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},H={...Z,transform:t=>e(0,1,t)},_={...Z,default:1},G=t=>Math.round(1e5*t)/1e5,J=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const Q=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>n=>Boolean("string"==typeof n&&Q.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),et=(t,e,n)=>s=>{if("string"!=typeof s)return s;const[i,r,a,o]=s.match(J);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},nt={...Z,transform:t=>Math.round((t=>e(0,255,t))(t))},st={test:tt("rgb","red"),parse:et("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+nt.transform(t)+", "+nt.transform(e)+", "+nt.transform(n)+", "+G(H.transform(s))+")"};const it={test:tt("#"),parse:function(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}},transform:st.transform},rt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),at=rt("deg"),ot=rt("%"),lt=rt("px"),ut=rt("vh"),ht=rt("vw"),ct=(()=>({...ot,parse:t=>ot.parse(t)/100,transform:t=>ot.transform(100*t)}))(),dt={test:tt("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+ot.transform(G(e))+", "+ot.transform(G(n))+", "+G(H.transform(s))+")"},pt={test:t=>st.test(t)||it.test(t)||dt.test(t),parse:t=>st.test(t)?st.parse(t):dt.test(t)?dt.parse(t):it.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?st.transform(t):dt.transform(t),getAnimatableNone:t=>{const e=pt.parse(t);return e.alpha=0,pt.transform(e)}},mt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const ft="number",yt="color",gt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function vt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const a=e.replace(gt,t=>(pt.test(t)?(s.color.push(r),i.push(yt),n.push(pt.parse(t))):t.startsWith("var(")?(s.var.push(r),i.push("var"),n.push(t)):(s.number.push(r),i.push(ft),n.push(parseFloat(t))),++r,"${}")).split("${}");return{values:n,split:a,indexes:s,types:i}}function bt(t){return vt(t).values}function wt(t){const{split:e,types:n}=vt(t),s=e.length;return t=>{let i="";for(let r=0;r<s;r++)if(i+=e[r],void 0!==t[r]){const e=n[r];i+=e===ft?G(t[r]):e===yt?pt.transform(t[r]):t[r]}return i}}const Tt=t=>"number"==typeof t?0:pt.test(t)?pt.getAnimatableNone(t):t;const Mt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(J)?.length||0)+(t.match(mt)?.length||0)>0},parse:bt,createTransformer:wt,getAnimatableNone:function(t){const e=bt(t);return wt(t)(e.map(Tt))}};function Vt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function At(t,e){return n=>n>0?e:t}const St=(t,e,n)=>t+(e-t)*n,xt=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},kt=[it,st,dt];function Ct(t){const e=(n=t,kt.find(t=>t.test(n)));var n;if(!Boolean(e))return!1;let s=e.parse(t);return e===dt&&(s=function({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,n/=100;let i=0,r=0,a=0;if(e/=100){const s=n<.5?n*(1+e):n+e-n*e,o=2*n-s;i=Vt(o,s,t+1/3),r=Vt(o,s,t),a=Vt(o,s,t-1/3)}else i=r=a=n;return{red:Math.round(255*i),green:Math.round(255*r),blue:Math.round(255*a),alpha:s}}(s)),s}const Ft=(t,e)=>{const n=Ct(t),s=Ct(e);if(!n||!s)return At(t,e);const i={...n};return t=>(i.red=xt(n.red,s.red,t),i.green=xt(n.green,s.green,t),i.blue=xt(n.blue,s.blue,t),i.alpha=St(n.alpha,s.alpha,t),st.transform(i))},Pt=new Set(["none","hidden"]);function Et(t,e){return n=>St(t,e,n)}function Ot(t){return"number"==typeof t?Et:"string"==typeof t?q(t)?At:pt.test(t)?Ft:Rt:Array.isArray(t)?Dt:"object"==typeof t?pt.test(t)?Ft:It:At}function Dt(t,e){const n=[...t],s=n.length,i=t.map((t,n)=>Ot(t)(t,e[n]));return t=>{for(let e=0;e<s;e++)n[e]=i[e](t);return n}}function It(t,e){const n={...t,...e},s={};for(const i in n)void 0!==t[i]&&void 0!==e[i]&&(s[i]=Ot(t[i])(t[i],e[i]));return t=>{for(const e in s)n[e]=s[e](t);return n}}const Rt=(t,e)=>{const n=Mt.createTransformer(e),s=vt(t),i=vt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?Pt.has(t)&&!i.values.length||Pt.has(e)&&!s.values.length?function(t,e){return Pt.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):l(Dt(function(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],a=t.indexes[r][s[r]],o=t.values[a]??0;n[i]=o,s[r]++}return n}(s,i),i.values),n):At(t,e)};function Bt(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return St(t,e,n);return Ot(t)(t,e)}const Nt=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>B.update(e,t),stop:()=>N(e),now:()=>K.isProcessing?K.timestamp:$.now()}},Kt=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let e=0;e<i;e++)s+=Math.round(1e4*t(e/(i-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},jt=2e4;function Wt(t){let e=0;let n=t.next(e);for(;!n.done&&e<jt;)e+=50,n=t.next(e);return e>=jt?1/0:e}function Yt(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(Wt(s),jt);return{type:"keyframes",ease:t=>s.next(i*t).value/e,duration:d(i)}}function $t(t,e,n){const s=Math.max(e-5,0);return p(n-t(s),e-s)}const Lt=100,Ut=10,Xt=1,qt=0,zt=800,Zt=.3,Ht=.3,_t={granular:.01,default:2},Gt={granular:.005,default:.5},Jt=.01,Qt=10,te=.05,ee=1,ne=.001;function se({duration:t=zt,bounce:n=Zt,velocity:s=qt,mass:i=Xt}){let r,a,o=1-n;o=e(te,ee,o),t=e(Jt,Qt,d(t)),o<1?(r=e=>{const n=e*o,i=n*t,r=n-s,a=re(e,o),l=Math.exp(-i);return ne-r/a*l},a=e=>{const n=e*o*t,i=n*s+s,a=Math.pow(o,2)*Math.pow(e,2)*t,l=Math.exp(-n),u=re(Math.pow(e,2),o);return(-r(e)+ne>0?-1:1)*((i-a)*l)/u}):(r=e=>Math.exp(-e*t)*((e-s)*t+1)-.001,a=e=>Math.exp(-e*t)*(t*t*(s-e)));const l=function(t,e,n){let s=n;for(let n=1;n<ie;n++)s-=t(s)/e(s);return s}(r,a,5/t);if(t=c(t),isNaN(l))return{stiffness:Lt,damping:Ut,duration:t};{const e=Math.pow(l,2)*i;return{stiffness:e,damping:2*o*Math.sqrt(i*e),duration:t}}}const ie=12;function re(t,e){return t*Math.sqrt(1-e*e)}const ae=["duration","bounce"],oe=["stiffness","damping","mass"];function le(t,e){return e.some(e=>void 0!==t[e])}function ue(t=Ht,n=Zt){const s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:n}:t;let{restSpeed:i,restDelta:r}=s;const a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:h,mass:p,duration:m,velocity:f,isResolvedFromDuration:y}=function(t){let n={velocity:qt,stiffness:Lt,damping:Ut,mass:Xt,isResolvedFromDuration:!1,...t};if(!le(t,oe)&&le(t,ae))if(t.visualDuration){const s=t.visualDuration,i=2*Math.PI/(1.2*s),r=i*i,a=2*e(.05,1,1-(t.bounce||0))*Math.sqrt(r);n={...n,mass:Xt,stiffness:r,damping:a}}else{const e=se(t);n={...n,...e,mass:Xt},n.isResolvedFromDuration=!0}return n}({...s,velocity:-d(s.velocity||0)}),g=f||0,v=h/(2*Math.sqrt(u*p)),b=o-a,w=d(Math.sqrt(u/p)),T=Math.abs(b)<5;let M;if(i||(i=T?_t.granular:_t.default),r||(r=T?Gt.granular:Gt.default),v<1){const t=re(w,v);M=e=>{const n=Math.exp(-v*w*e);return o-n*((g+v*w*b)/t*Math.sin(t*e)+b*Math.cos(t*e))}}else if(1===v)M=t=>o-Math.exp(-w*t)*(b+(g+w*b)*t);else{const t=w*Math.sqrt(v*v-1);M=e=>{const n=Math.exp(-v*w*e),s=Math.min(t*e,300);return o-n*((g+v*w*b)*Math.sinh(s)+t*b*Math.cosh(s))/t}}const V={calculatedDuration:y&&m||null,next:t=>{const e=M(t);if(y)l.done=t>=m;else{let n=0===t?g:0;v<1&&(n=0===t?c(g):$t(M,t,e));const s=Math.abs(n)<=i,a=Math.abs(o-e)<=r;l.done=s&&a}return l.value=l.done?o:e,l},toString:()=>{const t=Math.min(Wt(V),jt),e=Kt(e=>V.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return V}function he({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){const c=t[0],d={done:!1,value:c},p=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l;let m=n*e;const f=c+m,y=void 0===a?f:a(f);y!==f&&(m=y-c);const g=t=>-m*Math.exp(-t/s),v=t=>y+g(t),b=t=>{const e=g(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?y:n};let w,T;const M=t=>{var e;(e=d.value,void 0!==o&&e<o||void 0!==l&&e>l)&&(w=t,T=ue({keyframes:[d.value,p(d.value)],velocity:$t(v,t,d.value),damping:i,stiffness:r,restDelta:u,restSpeed:h}))};return M(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==w||(e=!0,b(t),M(t)),void 0!==w&&t>=w?T.next(t-w):(!e&&b(t),d)}}}function ce(t,s,{clamp:i=!0,ease:r,mixer:o}={}){const h=t.length;if(s.length,1===h)return()=>s[0];if(2===h&&s[0]===s[1])return()=>s[1];const c=t[0]===t[1];t[0]>t[h-1]&&(t=[...t].reverse(),s=[...s].reverse());const d=function(t,e,s){const i=[],r=s||n.mix||Bt,o=t.length-1;for(let n=0;n<o;n++){let s=r(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||a:e;s=l(t,s)}i.push(s)}return i}(s,r,o),p=d.length,m=e=>{if(c&&e<t[0])return s[0];let n=0;if(p>1)for(;n<t.length-2&&!(e<t[n+1]);n++);const i=u(t[n],t[n+1],e);return d[n](i)};return i?n=>m(e(t[0],t[h-1],n)):m}function de(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=u(0,e,s);t.push(St(n,1,i))}}function pe(t){const e=[0];return de(e,t.length-1),e}function me({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=C(s)?s.map(O):O(s),r={done:!1,value:e[0]},a=function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:pe(e),t),o=ce(a,e,{ease:Array.isArray(i)?i:(l=e,u=i,l.map(()=>u||k).splice(0,l.length-1))});var l,u;return{calculatedDuration:t,next:e=>(r.value=o(e),r.done=e>=t,r)}}ue.applyToOptions=t=>{const e=Yt(t,100,ue);return t.ease=e.ease,t.duration=c(e.duration),t.type="keyframes",t};const fe=t=>null!==t;function ye(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter(fe),a=i<0||e&&"loop"!==n&&e%2==1?0:r.length-1;return a&&void 0!==s?s:r[a]}const ge={decay:he,inertia:he,tween:me,keyframes:me,spring:ue};function ve(t){"string"==typeof t.type&&(t.type=ge[t.type])}class be{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const we=t=>t/100;class Te extends be{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==$.now()&&this.tick($.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;ve(t);const{type:e=me,repeat:n=0,repeatDelay:s=0,repeatType:i,velocity:r=0}=t;let{keyframes:a}=t;const o=e||me;o!==me&&"number"!=typeof a[0]&&(this.mixKeyframes=l(we,Bt(a[0],a[1])),a=[0,100]);const u=o({...t,keyframes:a});"mirror"===i&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-r})),null===u.calculatedDuration&&(u.calculatedDuration=Wt(u));const{calculatedDuration:h}=u;this.calculatedDuration=h,this.resolvedDuration=h+s,this.totalDuration=this.resolvedDuration*(n+1)-s,this.generator=u}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,n=!1){const{generator:s,totalDuration:i,mixKeyframes:r,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:l}=this;if(null===this.startTime)return s.next(0);const{delay:u=0,keyframes:h,repeat:c,repeatType:d,repeatDelay:p,type:m,onUpdate:f,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),n?this.currentTime=t:this.updateTime(t);const g=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let b=this.currentTime,w=s;if(c){const t=Math.min(this.currentTime,i)/o;let n=Math.floor(t),s=t%1;!s&&t>=1&&(s=1),1===s&&n--,n=Math.min(n,c+1);Boolean(n%2)&&("reverse"===d?(s=1-s,p&&(s-=p/o)):"mirror"===d&&(w=a)),b=e(0,1,s)*o}const T=v?{done:!1,value:h[0]}:w.next(b);r&&(T.value=r(T.value));let{done:M}=T;v||null===l||(M=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const V=null===this.holdTime&&("finished"===this.state||"running"===this.state&&M);return V&&m!==he&&(T.value=ye(h,this.options,y,this.speed)),f&&f(T.value),V&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return d(this.calculatedDuration)}get time(){return d(this.currentTime)}set time(t){t=c(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime($.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=d(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=Nt,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime($.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}const Me=t=>180*t/Math.PI,Ve=t=>{const e=Me(Math.atan2(t[1],t[0]));return Se(e)},Ae={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Ve,rotateZ:Ve,skewX:t=>Me(Math.atan(t[1])),skewY:t=>Me(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Se=t=>((t%=360)<0&&(t+=360),t),xe=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ke=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Ce={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:xe,scaleY:ke,scale:t=>(xe(t)+ke(t))/2,rotateX:t=>Se(Me(Math.atan2(t[6],t[5]))),rotateY:t=>Se(Me(Math.atan2(-t[2],t[0]))),rotateZ:Ve,rotate:Ve,skewX:t=>Me(Math.atan(t[4])),skewY:t=>Me(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Fe(t){return t.includes("scale")?1:0}function Pe(t,e){if(!t||"none"===t)return Fe(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=Ce,i=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=Ae,i=e}if(!i)return Fe(e);const r=s[e],a=i[1].split(",").map(Ee);return"function"==typeof r?r(a):a[r]}function Ee(t){return parseFloat(t.trim())}const Oe=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],De=(()=>new Set(Oe))(),Ie=t=>t===Z||t===lt,Re=new Set(["x","y","z"]),Be=Oe.filter(t=>!Re.has(t));const Ne={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Pe(e,"x"),y:(t,{transform:e})=>Pe(e,"y")};Ne.translateX=Ne.x,Ne.translateY=Ne.y;const Ke=new Set;let je=!1,We=!1,Ye=!1;function $e(){if(We){const t=Array.from(Ke).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return Be.forEach(n=>{const s=t.getValue(n);void 0!==s&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}We=!1,je=!1,Ke.forEach(t=>t.complete(Ye)),Ke.clear()}function Le(){Ke.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(We=!0)})}class Ue{constructor(t,e,n,s,i,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=s,this.element=i,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(Ke.add(this),je||(je=!0,B.read(Le),B.resolveKeyframes($e))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:s}=this;if(null===t[0]){const i=s?.get(),r=t[t.length-1];if(void 0!==i)t[0]=i;else if(n&&e){const s=n.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===i&&s.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),Ke.delete(this)}cancel(){"scheduled"===this.state&&(Ke.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const Xe=r(()=>void 0!==window.ScrollTimeline),qe={};function ze(t,e){const n=r(t);return()=>qe[e]??n()}const Ze=ze(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),He=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,_e={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:He([0,.65,.55,1]),circOut:He([.55,0,1,.45]),backIn:He([.31,.01,.66,-.59]),backOut:He([.33,1.53,.69,.99])};function Ge(t,e){return t?"function"==typeof t?Ze()?Kt(t,e):"ease-out":P(t)?He(t):Array.isArray(t)?t.map(t=>Ge(t,e)||_e.easeOut):_e[t]:void 0}function Je(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u=void 0){const h={[e]:n};l&&(h.offset=l);const c=Ge(o,i);Array.isArray(c)&&(h.easing=c);const d={delay:s,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};u&&(d.pseudoElement=u);return t.animate(h,d)}function Qe(t){return"function"==typeof t&&"applyToOptions"in t}class tn extends be{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:s,pseudoElement:i,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=Boolean(i),this.allowFlatten=r,this.options=t,t.type;const l=function({type:t,...e}){return Qe(t)&&Ze()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=Je(e,n,s,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){const t=ye(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return d(Number(t))}get time(){return d(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=c(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&Xe()?(this.animation.timeline=t,a):e(this)}}const en={anticipate:T,backInOut:w,circInOut:A};function nn(t){"string"==typeof t.ease&&t.ease in en&&(t.ease=en[t.ease])}class sn extends tn{constructor(t){nn(t),ve(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:s,element:i,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const a=new Te({...r,autoplay:!1}),o=c(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}const rn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Mt.test(t)&&"0"!==t||t.startsWith("url(")));function an(t){t.duration=0,t.type}const on=new Set(["opacity","clipPath","filter","transform"]),ln=r(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class un extends be{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=$.now();const c={autoplay:t,delay:e,type:n,repeat:s,repeatDelay:i,repeatType:r,name:o,motionValue:l,element:u,...h},d=u?.KeyframeResolver||Ue;this.keyframeResolver=new d(a,(t,e,n)=>this.onKeyframesResolved(t,e,c,!n),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,s,i){this.keyframeResolver=void 0;const{name:r,type:o,velocity:l,delay:u,isHandoff:h,onUpdate:c}=s;this.resolvedAt=$.now(),function(t,e,n,s){const i=t[0];if(null===i)return!1;if("display"===e||"visibility"===e)return!0;const r=t[t.length-1],a=rn(i,e),o=rn(r,e);return!(!a||!o)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||Qe(n))&&s)}(t,r,o,l)||(!n.instantAnimations&&u||c?.(ye(t,s,e)),t[0]=t[t.length-1],an(s),s.repeat=0);const d={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...s,keyframes:t},p=!h&&function(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:r,type:a}=t,o=e?.owner?.current;if(!(o instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=e.owner.getProps();return ln()&&n&&on.has(n)&&("transform"!==n||!u)&&!l&&!s&&"mirror"!==i&&0!==r&&"inertia"!==a}(d)?new sn({...d,element:d.motionValue.owner.current}):new Te(d);p.finished.then(()=>this.notifyFinished()).catch(a),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Ye=!0,Le(),$e(),Ye=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}class hn{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class cn extends hn{then(t,e){return this.finished.finally(t).then(()=>{})}}const dn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function pn(t,e,n=1){const[i,r]=function(t){const e=dn.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}(t);if(!i)return;const a=window.getComputedStyle(e).getPropertyValue(i);if(a){const t=a.trim();return s(t)?parseFloat(t):t}return q(r)?pn(r,e,n+1):r}function mn(t,e){return t?.[e]??t?.default??t}const fn=new Set(["width","height","top","left","right","bottom",...Oe]),yn=t=>e=>e.test(t),gn=[Z,lt,ot,at,ht,ut,{test:t=>"auto"===t,parse:t=>t}],vn=t=>gn.find(yn(t));function bn(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||i(t))}const wn=new Set(["brightness","contrast","saturate","opacity"]);function Tn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[s]=n.match(J)||[];if(!s)return t;const i=n.replace(s,"");let r=wn.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Mn=/\b([a-z-]*)\(.*?\)/gu,Vn={...Mt,getAnimatableNone:t=>{const e=t.match(Mn);return e?e.map(Tn).join(" "):t}},An={...Z,transform:Math.round},Sn={borderWidth:lt,borderTopWidth:lt,borderRightWidth:lt,borderBottomWidth:lt,borderLeftWidth:lt,borderRadius:lt,radius:lt,borderTopLeftRadius:lt,borderTopRightRadius:lt,borderBottomRightRadius:lt,borderBottomLeftRadius:lt,width:lt,maxWidth:lt,height:lt,maxHeight:lt,top:lt,right:lt,bottom:lt,left:lt,padding:lt,paddingTop:lt,paddingRight:lt,paddingBottom:lt,paddingLeft:lt,margin:lt,marginTop:lt,marginRight:lt,marginBottom:lt,marginLeft:lt,backgroundPositionX:lt,backgroundPositionY:lt,...{rotate:at,rotateX:at,rotateY:at,rotateZ:at,scale:_,scaleX:_,scaleY:_,scaleZ:_,skew:at,skewX:at,skewY:at,distance:lt,translateX:lt,translateY:lt,translateZ:lt,x:lt,y:lt,z:lt,perspective:lt,transformPerspective:lt,opacity:H,originX:ct,originY:ct,originZ:lt},zIndex:An,fillOpacity:H,strokeOpacity:H,numOctaves:An},xn={...Sn,color:pt,backgroundColor:pt,outlineColor:pt,fill:pt,stroke:pt,borderColor:pt,borderTopColor:pt,borderRightColor:pt,borderBottomColor:pt,borderLeftColor:pt,filter:Vn,WebkitFilter:Vn},kn=t=>xn[t];function Cn(t,e){let n=kn(t);return n!==Vn&&(n=Mt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Fn=new Set(["auto","none","0"]);class Pn extends Ue{constructor(t,e,n,s,i){super(t,e,n,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let s=t[n];if("string"==typeof s&&(s=s.trim(),q(s))){const i=pn(s,e.current);void 0!==i&&(t[n]=i),n===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!fn.has(n)||2!==t.length)return;const[s,i]=t,r=vn(s),a=vn(i);if(r!==a)if(Ie(r)&&Ie(a))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else Ne[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||bn(t[e]))&&n.push(e);n.length&&function(t,e,n){let s,i=0;for(;i<t.length&&!s;){const e=t[i];"string"==typeof e&&!Fn.has(e)&&vt(e).values.length&&(s=t[i]),i++}if(s&&n)for(const i of e)t[i]=Cn(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ne[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];void 0!==s&&t.getValue(n,s).jump(s,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const i=n.length-1,r=n[i];n[i]=Ne[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}const En=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class On{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{const e=$.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=$.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new h);const n=this.events[t].add(e);return"change"===t?()=>{n(),B.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=$.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return p(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Dn(t,e){return new On(t,e)}const{schedule:In,cancel:Rn}=R(queueMicrotask,!1);function Bn(t){return"object"==typeof(e=t)&&null!==e&&"ownerSVGElement"in t;var e}const Nn=t=>Boolean(t&&t.getVelocity),Kn=[...gn,pt,Mt];function jn(t){return"object"==typeof t&&!Array.isArray(t)}function Wn(t,e,n,s){return"string"==typeof t&&jn(e)?function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t,n,s):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function Yn(t,e,n){return t*(e+1)}function $n(t,e,n,s){return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:e.startsWith("<")?Math.max(0,n+parseFloat(e.slice(1))):s.get(e)??t}function Ln(e,n,s,i,r,a){!function(e,n,s){for(let i=0;i<e.length;i++){const r=e[i];r.at>n&&r.at<s&&(t(e,r),i--)}}(e,r,a);for(let t=0;t<n.length;t++)e.push({value:n[t],at:St(r,a,i[t]),easing:F(s,t)})}function Un(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function Xn(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function qn(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function zn(t,e){return e[t]||(e[t]=[]),e[t]}function Zn(t){return Array.isArray(t)?t:[t]}function Hn(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const _n=t=>"number"==typeof t,Gn=t=>t.every(_n),Jn=new WeakMap;function Qn(t){const e=[{},{}];return t?.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function ts(t,e,n,s){if("function"==typeof e){const[i,r]=Qn(s);e=e(void 0!==n?n:t.custom,i,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[i,r]=Qn(s);e=e(void 0!==n?n:t.custom,i,r)}return e}function es(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Dn(n))}function ns(t){return(t=>Array.isArray(t))(t)?t[t.length-1]||0:t}function ss(t,e){const n=function(t,e,n){const s=t.getProps();return ts(s,e,void 0!==n?n:s.custom,t)}(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const e in r){es(t,e,ns(r[e]))}}function is(t,e){const s=t.getValue("willChange");if(i=s,Boolean(Nn(i)&&i.add))return s.add(e);if(!s&&n.WillChange){const s=new n.WillChange("auto");t.addValue("willChange",s),s.add(e)}var i}const rs=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),as="data-"+rs("framerAppearId");function os(t){return t.props[as]}const ls=t=>null!==t;const us={type:"spring",stiffness:500,damping:25,restSpeed:10},hs={type:"keyframes",duration:.8},cs={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ds=(t,{keyframes:e})=>e.length>2?hs:De.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:us:cs;const ps=(t,e,s,i={},r,a)=>o=>{const l=mn(i,t)||{},u=l.delay||i.delay||0;let{elapsed:h=0}=i;h-=c(u);const d={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-h,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:a?void 0:r};(function({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length})(l)||Object.assign(d,ds(t,d)),d.duration&&(d.duration=c(d.duration)),d.repeatDelay&&(d.repeatDelay=c(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let p=!1;if((!1===d.type||0===d.duration&&!d.repeatDelay)&&(an(d),0===d.delay&&(p=!0)),(n.instantAnimations||n.skipAnimations)&&(p=!0,an(d),d.delay=0),d.allowFlatten=!l.type&&!l.ease,p&&!a&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(ls),r=e&&"loop"!==n&&e%2==1?0:i.length-1;return r&&void 0!==s?s:i[r]}(d.keyframes,l);if(void 0!==t)return void B.update(()=>{d.onUpdate(t),d.onComplete()})}return l.isSync?new Te(d):new un(d)};function ms({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,s}function fs(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...o}=e;s&&(r=s);const l=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const e in o){const s=t.getValue(e,t.latestValues[e]??null),i=o[e];if(void 0===i||u&&ms(u,e))continue;const a={delay:n,...mn(r||{},e)},h=s.get();if(void 0!==h&&!s.isAnimating&&!Array.isArray(i)&&i===h&&!a.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const n=os(t);if(n){const t=window.MotionHandoffAnimation(n,e,B);null!==t&&(a.startTime=t,c=!0)}}is(t,e),s.start(ps(e,s,i,t.shouldReduceMotion&&fn.has(e)?{type:!1}:a,t,c));const d=s.animation;d&&l.push(d)}return a&&Promise.all(l).then(()=>{B.update(()=>{a&&ss(t,a)})}),l}const ys={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},gs={};for(const t in ys)gs[t]={isEnabled:e=>ys[t].some(t=>!!e[t])};const vs=()=>({x:{min:0,max:0},y:{min:0,max:0}}),bs="undefined"!=typeof window,ws={current:null},Ts={current:!1};const Ms=["initial","animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"];function Vs(t){return null!==(e=t.animate)&&"object"==typeof e&&"function"==typeof e.start||Ms.some(e=>function(t){return"string"==typeof t||Array.isArray(t)}(t[e]));var e}const As=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Ss{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:s,blockInitialAnimation:i,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ue,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=$.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,B.render(this.render,!1,!0))};const{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=Boolean(i),this.isControllingVariants=Vs(e),this.isVariantNode=function(t){return Boolean(Vs(t)||t.variants)}(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in h){const e=h[t];void 0!==o[t]&&Nn(e)&&e.set(o[t])}}mount(t){this.current=t,Jn.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),Ts.current||function(){if(Ts.current=!0,bs)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ws.current=t.matches;t.addEventListener("change",e),e()}else ws.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ws.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),N(this.notifyUpdate),N(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=De.has(t);n&&this.onBindTransform&&this.onBindTransform();const s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&B.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let i;window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in gs){const e=gs[t];if(!e)continue;const{isEnabled:n,Feature:s}=e;if(!this.features[t]&&s&&n(this.props)&&(this.features[t]=new s(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<As.length;e++){const n=As[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const s=t["on"+n];s&&(this.propEventSubscriptions[n]=this.on(n,s))}this.prevMotionValues=function(t,e,n){for(const s in e){const i=e[s],r=n[s];if(Nn(i))t.addValue(s,i);else if(Nn(r))t.addValue(s,Dn(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const e=t.getValue(s);!0===e.liveStyle?e.jump(i):e.hasAnimated||e.set(i)}else{const e=t.getStaticValue(s);t.addValue(s,Dn(void 0!==e?e:i,{owner:t}))}}for(const s in n)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=Dn(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var r;return null!=n&&("string"==typeof n&&(s(n)||i(n))?n=parseFloat(n):(r=n,!Kn.find(yn(r))&&Mt.test(e)&&(n=Cn(t,e))),this.setBaseTarget(t,Nn(n)?n.get():n)),Nn(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const s=ts(this.props,e,this.presenceContext?.custom);s&&(n=s[t])}if(e&&void 0!==n)return n;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||Nn(s)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new h),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){In.render(this.render)}}class xs extends Ss{constructor(){super(...arguments),this.KeyframeResolver=Pn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Nn(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}const ks={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Cs=Oe.length;function Fs(t,e,n){const{style:s,vars:i,transformOrigin:r}=t;let a=!1,o=!1;for(const t in e){const n=e[t];if(De.has(t))a=!0;else if(U(t))i[t]=n;else{const e=En(n,Sn[t]);t.startsWith("origin")?(o=!0,r[t]=e):s[t]=e}}if(e.transform||(a||n?s.transform=function(t,e,n){let s="",i=!0;for(let r=0;r<Cs;r++){const a=Oe[r],o=t[a];if(void 0===o)continue;let l=!0;if(l="number"==typeof o?o===(a.startsWith("scale")?1:0):0===parseFloat(o),!l||n){const t=En(o,Sn[a]);l||(i=!1,s+=`${ks[a]||a}(${t}) `),n&&(e[a]=t)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}(e,t.transform,n):s.transform&&(s.transform="none")),o){const{originX:t="50%",originY:e="50%",originZ:n=0}=r;s.transformOrigin=`${t} ${e} ${n}`}}function Ps(t,{style:e,vars:n},s,i){const r=t.style;let a;for(a in e)r[a]=e[a];for(a in i?.applyProjectionStyles(r,s),n)r.setProperty(a,n[a])}const Es={};function Os(t,{layout:e,layoutId:n}){return De.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Es[t]||"opacity"===t)}function Ds(t,e,n){const{style:s}=t,i={};for(const r in s)(Nn(s[r])||e.style&&Nn(e.style[r])||Os(r,t)||void 0!==n?.getValue(r)?.liveStyle)&&(i[r]=s[r]);return i}class Is extends xs{constructor(){super(...arguments),this.type="html",this.renderInstance=Ps}readValueFromInstance(t,e){if(De.has(e))return this.projection?.isProjecting?Fe(e):((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Pe(n,e)})(t,e);{const s=(n=t,window.getComputedStyle(n)),i=(U(e)?s.getPropertyValue(e):s[e])||0;return"string"==typeof i?i.trim():i}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return function(t,e){return function({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}(t,e)}build(t,e,n){Fs(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return Ds(t,e,n)}}class Rs extends Ss{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(function(t,e){return t in e}(e,t)){const n=t[e];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}const Bs={offset:"stroke-dashoffset",array:"stroke-dasharray"},Ns={offset:"strokeDashoffset",array:"strokeDasharray"};function Ks(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:r=1,pathOffset:a=0,...o},l,u,h){if(Fs(t,o,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==n&&(c.y=n),void 0!==s&&(c.scale=s),void 0!==i&&function(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?Bs:Ns;t[r.offset]=lt.transform(-s);const a=lt.transform(e),o=lt.transform(n);t[r.array]=`${a} ${o}`}(c,i,r,a,!1)}const js=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class Ws extends xs{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=vs}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(De.has(e)){const t=kn(e);return t&&t.default||0}return e=js.has(e)?e:rs(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return function(t,e,n){const s=Ds(t,e,n);for(const n in t)(Nn(t[n])||Nn(e[n]))&&(s[-1!==Oe.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]);return s}(t,e,n)}build(t,e,n){Ks(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,s){!function(t,e,n,s){Ps(t,e,void 0,s);for(const n in e.attrs)t.setAttribute(js.has(n)?n:rs(n),e.attrs[n])}(t,e,0,s)}mount(t){var e;this.isSVGTag="string"==typeof(e=t.tagName)&&"svg"===e.toLowerCase(),super.mount(t)}}function Ys(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=Bn(t)&&!function(t){return Bn(t)&&"svg"===t.tagName}(t)?new Ws(e):new Is(e);n.mount(t),Jn.set(t,n)}function $s(t){const e=new Rs({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),Jn.set(t,e)}function Ls(t,e,n,s){const i=[];if(function(t,e){return Nn(t)||"number"==typeof t||"string"==typeof t&&!jn(e)}(t,e))i.push(function(t,e,n){const s=Nn(t)?t:Dn(t);return s.start(ps("",s,e,n)),s.animation}(t,jn(e)&&e.default||e,n&&n.default||n));else{const r=Wn(t,e,s),a=r.length;for(let t=0;t<a;t++){const s=r[t],o=s instanceof Element?Ys:$s;Jn.has(s)||o(s);const l=Jn.get(s),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,a)),i.push(...fs(l,{...e,transition:u},{}))}}return i}function Us(t,e,n){const s=[],i=function(t,{defaultTransition:e={},...n}={},s,i){const r=e.duration||.3,a=new Map,o=new Map,l={},h=new Map;let d=0,p=0,m=0;for(let n=0;n<t.length;n++){const a=t[n];if("string"==typeof a){h.set(a,p);continue}if(!Array.isArray(a)){h.set(a.name,$n(p,a.at,d,h));continue}let[u,f,y={}]=a;void 0!==y.at&&(p=$n(p,y.at,d,h));let g=0;const v=(t,n,s,a=0,o=0)=>{const l=Zn(t),{delay:u=0,times:h=pe(l),type:d="keyframes",repeat:f,repeatType:y,repeatDelay:v=0,...b}=n;let{ease:w=e.ease||"easeOut",duration:T}=n;const M="function"==typeof u?u(a,o):u,V=l.length,A=Qe(d)?d:i?.[d||"keyframes"];if(V<=2&&A){let t=100;if(2===V&&Gn(l)){const e=l[1]-l[0];t=Math.abs(e)}const e={...b};void 0!==T&&(e.duration=c(T));const n=Yt(e,t,A);w=n.ease,T=n.duration}T??(T=r);const S=p+M;1===h.length&&0===h[0]&&(h[1]=1);const x=h.length-l.length;if(x>0&&de(h,x),1===l.length&&l.unshift(null),f){T=Yn(T,f);const t=[...l],e=[...h];w=Array.isArray(w)?[...w]:[w];const n=[...w];for(let s=0;s<f;s++){l.push(...t);for(let i=0;i<t.length;i++)h.push(e[i]+(s+1)),w.push(0===i?"linear":F(n,i-1))}Un(h,f)}const k=S+T;Ln(s,l,w,h,S,k),g=Math.max(M+T,g),m=Math.max(k,m)};if(Nn(u))v(f,y,zn("default",qn(u,o)));else{const t=Wn(u,f,s,l),e=t.length;for(let n=0;n<e;n++){const s=qn(t[n],o);for(const t in f)v(f[t],Hn(y,t),zn(t,s),n,e)}}d=p,p+=g}return o.forEach((t,s)=>{for(const i in t){const r=t[i];r.sort(Xn);const o=[],l=[],h=[];for(let t=0;t<r.length;t++){const{at:e,value:n,easing:s}=r[t];o.push(n),l.push(u(0,m,e)),h.push(s||"easeOut")}0!==l[0]&&(l.unshift(0),o.unshift(o[0]),h.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),o.push(null)),a.has(s)||a.set(s,{keyframes:{},transition:{}});const c=a.get(s);c.keyframes[i]=o,c.transition[i]={...e,duration:m,ease:h,times:l,...n}}}),a}(t,e,n,{spring:ue});return i.forEach(({keyframes:t,transition:e},n)=>{s.push(...Ls(n,t,e))}),s}function Xs(e){return function(n,s,i){let r=[];var a;a=n,r=Array.isArray(a)&&a.some(Array.isArray)?Us(n,s,e):Ls(n,s,i,e);const o=new cn(r);return e&&(e.animations.push(o),o.finished.then(()=>{t(e.animations,o)})),o}}const qs=Xs();export{qs as animate,Xs as createScopedAnimate};
