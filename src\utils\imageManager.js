// 图片资源管理器
export const ImagePaths = {
  // 场景背景图
  scenes: {
    bedroom: '/images/scenes/bedroom.jpg',
    garden: '/images/scenes/garden.jpg',
    street: '/images/scenes/street.jpg',
    balcony: '/images/scenes/balcony.jpg'
  },
  
  // 猫咪图片
  cats: {
    orange: '/images/cats/orange-cat.png',
    white: '/images/cats/white-cat.png',
    black: '/images/cats/black-cat.png',
    tabby: '/images/cats/tabby-cat.png',
    persian: '/images/cats/persian-cat.png',
    siamese: '/images/cats/siamese-cat.png'
  },
  
  // 可交互物品
  objects: {
    pillow: '/images/objects/pillow.png',
    curtain: '/images/objects/curtain.png',
    bookshelf: '/images/objects/bookshelf.png',
    bush: '/images/objects/bush.png',
    tree: '/images/objects/tree.png',
    flowers: '/images/objects/flowers.png',
    sofa: '/images/objects/sofa.png',
    table: '/images/objects/table.png'
  },
  
  // UI元素
  ui: {
    logo: '/images/ui/logo.png',
    catPaw: '/images/ui/cat-paw.png',
    heart: '/images/ui/heart.png',
    star: '/images/ui/star.png',
    trophy: '/images/ui/trophy.png'
  },
  
  // 特效图片
  effects: {
    sparkle: '/images/effects/sparkle.png',
    star: '/images/effects/star.png',
    heart: '/images/effects/heart.png',
    confetti: '/images/effects/confetti.png'
  }
};

// 图片预加载函数
export const preloadImages = (imagePaths) => {
  return Promise.all(
    imagePaths.map(path => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(path);
        img.onerror = () => reject(new Error(`Failed to load image: ${path}`));
        img.src = path;
      });
    })
  );
};

// 预加载所有游戏图片
export const preloadAllGameImages = async () => {
  const allPaths = [
    ...Object.values(ImagePaths.scenes),
    ...Object.values(ImagePaths.cats),
    ...Object.values(ImagePaths.objects),
    ...Object.values(ImagePaths.ui),
    ...Object.values(ImagePaths.effects)
  ];
  
  try {
    await preloadImages(allPaths);
    console.log('All game images preloaded successfully');
    return true;
  } catch (error) {
    console.warn('Some images failed to preload:', error);
    return false;
  }
};

// 获取图片URL的辅助函数
export const getImageUrl = (category, name) => {
  return ImagePaths[category]?.[name] || '';
};

// 检查图片是否存在
export const checkImageExists = (url) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
};

// 获取图片尺寸
export const getImageDimensions = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
    img.src = url;
  });
};

// 创建响应式图片组件的辅助函数
export const createResponsiveImageStyle = (imagePath, options = {}) => {
  const {
    backgroundSize = 'cover',
    backgroundPosition = 'center',
    backgroundRepeat = 'no-repeat'
  } = options;
  
  return {
    backgroundImage: `url(${imagePath})`,
    backgroundSize,
    backgroundPosition,
    backgroundRepeat
  };
};

// 图片懒加载Hook（可选）
export const useLazyImage = (src) => {
  const [imageSrc, setImageSrc] = React.useState(null);
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [isError, setIsError] = React.useState(false);
  
  React.useEffect(() => {
    const img = new Image();
    
    img.onload = () => {
      setImageSrc(src);
      setIsLoaded(true);
    };
    
    img.onerror = () => {
      setIsError(true);
    };
    
    img.src = src;
  }, [src]);
  
  return { imageSrc, isLoaded, isError };
};
