import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './ParticleEffect.css';

const ParticleEffect = ({ 
  x, 
  y, 
  type = 'stars', 
  count = 8, 
  duration = 2000,
  onComplete 
}) => {
  const [particles, setParticles] = useState([]);

  useEffect(() => {
    // 生成粒子
    const newParticles = Array.from({ length: count }, (_, index) => ({
      id: index,
      x: x,
      y: y,
      angle: (360 / count) * index,
      distance: Math.random() * 100 + 50,
      scale: Math.random() * 0.5 + 0.5,
      emoji: getParticleEmoji(type),
      delay: Math.random() * 0.2
    }));

    setParticles(newParticles);

    // 清理
    const timer = setTimeout(() => {
      if (onComplete) onComplete();
    }, duration);

    return () => clearTimeout(timer);
  }, [x, y, type, count, duration, onComplete]);

  const getParticleEmoji = (type) => {
    const emojiSets = {
      stars: ['⭐', '✨', '💫', '🌟'],
      hearts: ['💖', '💕', '💗', '💝'],
      cats: ['🐱', '😸', '😻', '🙀'],
      celebration: ['🎉', '🎊', '✨', '🎈'],
      sparkles: ['✨', '💫', '⭐', '🌟']
    };

    const emojis = emojiSets[type] || emojiSets.stars;
    return emojis[Math.floor(Math.random() * emojis.length)];
  };

  return (
    <div className="particle-effect-container">
      <AnimatePresence>
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            className="particle"
            style={{
              left: particle.x,
              top: particle.y,
              fontSize: `${particle.scale * 1.5}rem`
            }}
            initial={{
              x: 0,
              y: 0,
              scale: 0,
              opacity: 1,
              rotate: 0
            }}
            animate={{
              x: Math.cos(particle.angle * Math.PI / 180) * particle.distance,
              y: Math.sin(particle.angle * Math.PI / 180) * particle.distance,
              scale: particle.scale,
              opacity: 0,
              rotate: 360
            }}
            transition={{
              duration: duration / 1000,
              delay: particle.delay,
              ease: "easeOut"
            }}
            exit={{
              opacity: 0,
              scale: 0
            }}
          >
            {particle.emoji}
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

// 预设的粒子效果组件
export const StarBurst = ({ x, y, onComplete }) => (
  <ParticleEffect 
    x={x} 
    y={y} 
    type="stars" 
    count={8} 
    duration={1500}
    onComplete={onComplete}
  />
);

export const HeartBurst = ({ x, y, onComplete }) => (
  <ParticleEffect 
    x={x} 
    y={y} 
    type="hearts" 
    count={6} 
    duration={2000}
    onComplete={onComplete}
  />
);

export const CatCelebration = ({ x, y, onComplete }) => (
  <ParticleEffect 
    x={x} 
    y={y} 
    type="cats" 
    count={5} 
    duration={2500}
    onComplete={onComplete}
  />
);

export const SparkleEffect = ({ x, y, onComplete }) => (
  <ParticleEffect 
    x={x} 
    y={y} 
    type="sparkles" 
    count={12} 
    duration={1800}
    onComplete={onComplete}
  />
);

export default ParticleEffect;
